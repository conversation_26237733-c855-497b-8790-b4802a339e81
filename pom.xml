<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ruijing.order</groupId>
        <artifactId>order-base-dependency</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>com.ruijing.sync</groupId>
    <artifactId>msharp-search-sync-order-service</artifactId>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>store-search-sync-order-api</module>
        <module>store-search-sync-order-server</module>
    </modules>
    <packaging>pom</packaging>

    <profiles>
        <profile>
            <id>jdk-1.8</id>
            <activation>
                <activeByDefault>true</activeByDefault>
                <jdk>1.8</jdk>
            </activation>
            <properties>
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
                <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
            </properties>
        </profile></profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.ruijing.plugin</groupId>
                <artifactId>api-package-plugin</artifactId>
                <version>1.1.0-SNAPSHOT</version>
                <configuration>
                    <!--项目名称，必填，需要唯一，未在数据库中的不允许使用-->
                    <appkey>store-search-sync-order-service</appkey>
                    <!--项目版本号，不填默认"1.0.0"-->
                    <version>1.0-SNAPSHOT</version>
                    <!--是否跳过swagger生成，不填默认false-->
                    <skipSwaggerGeneration>false</skipSwaggerGeneration>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <snapshotRepository>
            <id>snapshots</id>
            <name>nexus-snapshots</name>
            <url>http://nexus.rj-info.com/repository/maven-snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://nexus.rj-info.com/repository/maven-releases/</url>
        </repository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://nexus.rj-info.com/repository/maven-snapshots/</url>
        </repository>
        <repository>
            <id>maven-public</id>
            <name>maven-public</name>
            <url>http://nexus.rj-info.com/repository/maven-public/</url>
        </repository>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://nexus.rj-info.com/repository/maven-releases/</url>
        </repository>

    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>maven-public</id>
            <name>maven-public</name>
            <url>http://nexus.rj-info.com/repository/maven-public/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>