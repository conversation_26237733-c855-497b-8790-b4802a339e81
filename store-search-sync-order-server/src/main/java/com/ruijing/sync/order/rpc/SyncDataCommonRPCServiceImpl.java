package com.ruijing.sync.order.rpc;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.sync.common.api.SyncDataCommonRPCService;
import com.ruijing.sync.common.dto.SyncDataRequestDTO;
import com.ruijing.sync.order.service.compensate.CheckAbnormalService;
import com.ruijing.sync.order.service.SyncFullService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: zhong<PERSON><PERSON>i
 * @create: 2020/12/7 15:09
 **/
@MSharpService
public class SyncDataCommonRPCServiceImpl implements SyncDataCommonRPCService {

    private static final String CAT_TYPE = "SyncDataCommonRPCServiceImpl";

    @Resource
    private SyncFullService syncFullService;

    @Resource
    private CheckAbnormalService checkAbnormalService;

    @Override
    public RemoteResponse<Boolean> syncData(SyncDataRequestDTO request) {
        syncFullService.syncFullData(request);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }

    /**
     * 检查特定时间段es与mysql数据库 订单状态或存在情况 不同的地方
     * @param request
     * @return 不同的订单id列表
     */
    @Override
    public RemoteResponse<List<Integer>> checkDBAndES(SyncDataRequestDTO request) {
        List<Integer> abnormalList = checkAbnormalService.checkAbnormalData(request);
        return RemoteResponse.<List<Integer>>custom().setData(abnormalList).setSuccess();
    }

    /**
     * 将es和mysql数据库不同的数据写到邮件（异步，无论如何先返回成功，错误在cat)
     * @param request
     * @return
     */
    @Override
    public RemoteResponse<Boolean> checkDBAndESWriteToExcel(SyncDataRequestDTO request) {
        AsyncExecutor.listenableRunAsync(() -> checkAbnormalService.writeAbnormalDataToExcel(request))
                .addFailureCallback(throwable -> {
                    Cat.logError(CAT_TYPE, "checkDBAndESWriteToExcel", "将es和mysql数据库不同的数据写到邮件", throwable);
                });
        return RemoteResponse.success();
    }
}
