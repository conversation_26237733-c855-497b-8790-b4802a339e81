package com.ruijing.sync.order.rpc;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.orderdetail.service.OrderDetailService;
import com.ruijing.sync.order.log.annotation.ServiceLog;
import com.ruijing.sync.order.log.enums.ServiceType;

import java.util.List;

/**
 * @description: 订单详情RPC 接口
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/11/19 17:12
 **/
@ServiceClient
public class OrderDetailRPCClient {

    @MSharpReference(remoteAppkey = "store-order-service")
    private OrderDetailService orderDetailService;

    @ServiceLog(description = "批量查询订单详情", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderDetailDTO> findByOrderIdList(OrderDetailReq request) {
        RemoteResponse<List<OrderDetailDTO>> response = orderDetailService.findOrderDetailByMasterIdList(request);
        Preconditions.isTrue(response.isSuccess(), "批量查询订单详情失败！" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    public List<OrderDetailDTO> findByOrderIdList(List<Integer> orderIdList) {
        OrderDetailReq request = new OrderDetailReq();
        request.setOrderMasterIdList(orderIdList);

        return this.findByOrderIdList(request);
    }
}
