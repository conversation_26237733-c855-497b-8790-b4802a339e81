package com.ruijing.sync.order.rpc;

import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.whitehole.database.dto.address.data.DeliveryOperationLogDTO;
import com.ruijing.order.whitehole.database.dto.address.request.DeliveryOperationLogRequestDTO;
import com.ruijing.order.whitehole.database.service.DeliveryOperationLogDataService;
import com.ruijing.sync.order.log.annotation.ServiceLog;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@ServiceClient
public class OrderWhiteHoleClient {

    @MSharpReference(remoteAppkey = "order-whitehole-service")
    private DeliveryOperationLogDataService deliveryOperationLogDataService;

    @ServiceLog(description = "查询代配送日志")
    public List<DeliveryOperationLogDTO> listOperationLogInOrderId(Collection<Integer> orderIdCollection){
        DeliveryOperationLogRequestDTO deliveryOperationLogRequestDTO = new DeliveryOperationLogRequestDTO();
        deliveryOperationLogRequestDTO.setOrderIdList(orderIdCollection);
        PageableResponse<List<DeliveryOperationLogDTO>>  pageableResponse = deliveryOperationLogDataService.listOperationLogInOrderId(deliveryOperationLogRequestDTO);
        Preconditions.isTrue(pageableResponse.isSuccess(), "查询代配送操作日志失败");
        return pageableResponse.getData();
    }

}
