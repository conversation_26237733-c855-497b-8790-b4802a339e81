package com.ruijing.sync.order.utils;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2022/8/16 17:07
 */
public class ExcelUtils {

    /**
     * 单元格样式
     * @return
     */
    public static HorizontalCellStyleStrategy getCellStyleStrategy() {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为黄色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        WriteFont headWriteFont = new WriteFont();
        // 字体大小
        headWriteFont.setFontHeightInPoints((short)15);
        headWriteCellStyle.setWriteFont(headWriteFont);

        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short)11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headWriteCellStyle,contentWriteCellStyle);
    }

    /**
     * 记录订单id和订单号
     * @param masterInfoList
     * @return
     */
    public static List<List<Object>> constructAbnormalOrderRecord(List<OrderMasterDTO> masterInfoList) {
        List<List<Object>> resultList = new ArrayList<List<Object>>(masterInfoList.size());
        for (OrderMasterDTO orderMasterDTO : masterInfoList) {
            List<Object> curLine = new ArrayList<>();
            curLine.add(orderMasterDTO.getId());
            curLine.add(orderMasterDTO.getForderno());
            resultList.add(curLine);
        }
        return resultList;
    }

    /**
     * 生成好的excel文件转为byte
     * @param tempAbnormalFile
     * @return
     */
    public static byte[] getBytesFromFile(File tempAbnormalFile) {
        try {
            FileInputStream fis = new FileInputStream(tempAbnormalFile);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int n;
            while ((n = fis.read(b)) != -1) {
                baos.write(b, 0, n);
            }
            fis.close();
            byte[] data = baos.toByteArray();
            baos.close();
            return data;
        } catch (Exception e) {
            Cat.logError("getBytesFromFile将excel文件转换为byte[]失败", e);
            byte[] emptyBytes = new byte[0];
            return emptyBytes;
        }
    }
}
