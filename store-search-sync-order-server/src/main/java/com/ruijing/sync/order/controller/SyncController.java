package com.ruijing.sync.order.controller;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.sync.common.dto.SyncDataRequestDTO;
import com.ruijing.sync.order.log.annotation.ServiceLog;
import com.ruijing.sync.order.log.enums.OperationType;
import com.ruijing.sync.order.log.enums.ServiceType;
import com.ruijing.sync.order.service.compensate.CheckAbnormalService;
import com.ruijing.sync.order.service.DataImproveService;
import com.ruijing.sync.order.service.SyncFullService;
import com.ruijing.sync.order.service.compensate.CompensateDataService;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019/5/27 16:21
 */
@MSharpService()
@RpcMapping("/sync")
public class SyncController {

    @Resource
    private SyncFullService syncFullService;

    @Resource
    private CheckAbnormalService checkAbnormalService;

    @Resource
    private DataImproveService dataImproveService;

    @Resource
    private CompensateDataService compensateDataService;

    @RpcMapping("/full")
    public RemoteResponse<Integer> sync(@RequestBody SyncDataRequestDTO params) {
        Integer total = syncFullService.syncFullData(params);
        return RemoteResponse.<Integer>custom().setSuccess().setData(total);
    }

    @RpcMapping("/checkAbnormal")
    public RemoteResponse<Boolean> checkAbnormal(@RequestBody SyncDataRequestDTO request) {
        AsyncExecutor.listenableRunAsync(() -> checkAbnormalService.writeAbnormalDataToExcel(request))
                .addFailureCallback(throwable -> {
                    Cat.logError("SyncController", "checkDBAndESWriteToExcel", "将es和mysql数据库不同的数据写到邮件", throwable);
                });
        return RemoteResponse.success();
    }

    @RpcMapping("/supplementDeptParent")
    public RemoteResponse<Boolean> supplementDeptParent(@RequestBody SyncDataRequestDTO request) {
        // 校验放在主线程，运行再异步
        Boolean updateSuccess = dataImproveService.supplementDeptParentData(request);
        return Boolean.TRUE.equals(updateSuccess)
                ? RemoteResponse.success()
                : RemoteResponse.<Boolean>custom().setFailure("补充采购父一级部门失败");
    }

    @RpcMapping("/supplementDeptParentToEs")
    public RemoteResponse<Boolean> supplementDeptParentToEs(@RequestBody SyncDataRequestDTO request) {
        // 校验放在主线程，运行再异步
        Boolean updateSuccess = dataImproveService.findAndUpdateParentDept(request);
        return Boolean.TRUE.equals(updateSuccess)
                ? RemoteResponse.success()
                : RemoteResponse.<Boolean>custom().setFailure("补充采购父一级部门到es失败");
    }

    /**
     * 同步调用补全数据的方法
     * @param request
     * @return
     */
    @RpcMapping("/compensateAllData")
    @ServiceLog(description = "同步调用补全数据的方法", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> compensateAllData(@RequestBody SyncDataRequestDTO request) {
        Preconditions.isTrue("<EMAIL>".equalsIgnoreCase(request.getSlang()), "无权限补偿数据，请输入正确slang");
        compensateDataService.compensateAllData(request);
        return RemoteResponse.success();
    }
}
