package com.ruijing.sync.order.rpc;

import com.ruijing.fundamental.alarm.client.dingtalk.DingTalkAlarmServiceClient;
import com.ruijing.fundamental.alarm.dingtalk.client.request.At;
import com.ruijing.fundamental.alarm.dingtalk.client.request.Text;
import com.ruijing.fundamental.alarm.dingtalk.client.request.TextSendRequest;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Message;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.util.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2023/3/7 14:15
 */
@Service
public class DingTalkAlarmClient {

    private final Logger log = LoggerFactory.getLogger(getClass());

    /**
     * 发送钉钉通知
     * @param webHook
     * @param phoneList
     * @param content
     * @param dingAll
     */
    public void sendDingMessage(String webHook, List<String> phoneList, String content, boolean dingAll) {
        if (CollectionUtils.isEmpty(phoneList)) {
            return;
        }
        TextSendRequest textSendRequest = new TextSendRequest();
        Transaction catTransaction = Cat.newTransaction("DingTalkAlarmServiceImpl", "sendDingDing");
        try {
            Text text = new Text();
            text.setContent(content);
            At at = new At();
            at.setAtMobiles(phoneList);
            at.setAtAll(dingAll);

            textSendRequest.setAt(at);
            textSendRequest.setText(text);
            textSendRequest.setWebHook(webHook);
            boolean send = DingTalkAlarmServiceClient.getInstance().send(textSendRequest);
            catTransaction.setSuccessStatus();
            if (!send) {
                catTransaction.setStatus(Message.WARNING);
                catTransaction.addData(String.format("入参, textSendRequest: %s ", textSendRequest));
            }
        } catch (Exception e) {
            log.error("sendDingDing 异常,入参:{}", JsonUtils.toJsonIgnoreNull(textSendRequest), e);
            catTransaction.setStatus(e);
            catTransaction.addData(CatUtils.buildStackInfo("sendDingDing 异常", e));
            catTransaction.addData(String.format("入参, mobiles: %s;dingDingWebHook:%s ", phoneList, webHook));
        } finally {
            catTransaction.complete();
        }
    }
}
