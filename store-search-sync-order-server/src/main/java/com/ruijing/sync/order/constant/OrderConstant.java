package com.ruijing.sync.order.constant;

import com.ruijing.fundamental.common.collections.New;

import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class OrderConstant {
    public static final String OFFLINE = "1";

    public static final String CANAL_KEY = "order";

    public static final String FULL_KEY = "order_all";

    public static final Integer DOCKING_TYPE = 0;

    public static final Integer DOCKING_STATEMENT_TYPE = 1;

    /**
     * 增量同步的缓存key
     */
    public static final String ADDI_CACHE_KEY = "{stat}additive_id_key";

    /**
     * 全量同步的缓存key
     */
    public static final String FULL_CACHE_KEY = "{stat}full_id_key";

    /**
     * 全量补偿的缓存key
     */
    public static final String FULL_COMPENSATE_KEY = "{stat}full_compensate_key";

    /**
     * 增量补偿的缓存key
     */
    public static final String ADDI_COMPENSATE_KEY = "{stat}additive_compensate_key";

    /**
     * 是否已存在全量同步
     */
    public volatile static boolean synchronizingFlag = false;

    /**
     * 全量同步 是否开启redis
     */
    public volatile static boolean increRedisFlag = false;

    /**
     * 是否已存在全量补偿
     */
    public volatile static boolean compensationFlag = false;

    /**
     * 全量补偿 是否开启redis
     */
    public volatile static boolean compensationIncreRedisFlag = false;


    /**
     * 同步过程中的全局锁
     */
    public static Lock reentrantLock = new ReentrantLock(true);

    /**
     * 订单群的通知webhook
     */
    public static final String ORDER_GROUP_WEB_HOOK = "https://oapi.dingtalk.com/robot/send?access_token=f85ec4401acaf6b5c2295921f9c5ddfed285fbfe9700301d6b0caecce827428d";

    /**
     * 同步补偿告警电话
     */
    public static final List<String> SYNC_ALARM_AT_PHONE_LIST = New.list("18613078153", "18924554293");
}
