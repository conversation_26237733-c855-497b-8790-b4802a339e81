package com.ruijing.sync.order.rpc;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.ApplicationQueryDTO;
import com.ruijing.store.apply.service.application.ApplicationBaseService;
import com.ruijing.sync.order.log.annotation.ServiceLog;
import com.ruijing.sync.order.log.enums.ServiceType;

import java.util.List;

/**
 * @description: 采购单RPC 客户端
 * @author: zhongyulei
 * @create: 2020/11/19 16:15
 **/
@ServiceClient
public class PurchaseApplicationRPCClient {

    @MSharpReference(remoteAppkey="store-apply-service")
    private ApplicationBaseService applicationBaseService;

    @ServiceLog(description = "查询采购单", serviceType = ServiceType.COMMON_SERVICE)
    public List<ApplicationMasterDTO> findApplicationMaster(ApplicationQueryDTO requset) {
        RemoteResponse<List<ApplicationMasterDTO>> response = applicationBaseService.listApplicationMaster(requset);
        Preconditions.isTrue(response.isSuccess(), "查询采购单失败！" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    public List<ApplicationMasterDTO> findApplicationMaster(List<Long> appidList) {
        ApplicationQueryDTO request = new ApplicationQueryDTO();
        request.setApplyIds(appidList);
        request.setBusiness(ApplicationQueryDTO.BusinessEnum.APPLY_ID);

        return this.findApplicationMaster(request);
    }
}
