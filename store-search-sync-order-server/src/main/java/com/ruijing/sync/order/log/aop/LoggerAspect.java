package com.ruijing.sync.order.log.aop;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.sync.order.log.annotation.ServiceLog;
import com.ruijing.sync.order.log.enums.OperationType;
import com.ruijing.sync.order.log.enums.ServiceType;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 记录db操作的日志的 切面
 * @author: zhongyulei
 * @create: 2019/9/30 16:14
 **/
@Aspect
@Component
@CatAnnotation
@Order(10)
public class LoggerAspect {

    @PearlValue(key = "order.log.allInfoLog",defaultValue = "false")
    private boolean isAllInfoLog;

    /**
     * 需要打日志的simple类名
     */
    @PearlValue( key = "order.log.className",defaultValue = "[]")
    List<String> logClassNameList;

    /**
     * 需要打日志的方法名
     */
    @PearlValue( key = "order.log.methodName",defaultValue = "[]")
    List<String> logMethodNameList;

    @Pointcut("@annotation(com.ruijing.sync.order.log.annotation.ServiceLog) || @within(com.ruijing.sync.order.log.annotation.ServiceLog)")
    private void serviceLogOperation(){}

    /**
     * 日志切面。  枚举配置、或者配置中心配置类名和方法名的 会打印INFO日志
     * @param joinPoint
     * @return
     */
    @Around("serviceLogOperation()")
    private Object rpcServiceLoggerAround(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Logger logger = LoggerFactory.getLogger(className);

        MethodSignature signature = (MethodSignature)joinPoint.getSignature();
        ServiceLog annotation = signature.getMethod().getAnnotation(ServiceLog.class);

        if (annotation == null) {
            annotation = joinPoint.getTarget().getClass().getAnnotation(ServiceLog.class);
        }

        ServiceType logServiceEnum = annotation.serviceType();
        OperationType operationEnum = annotation.operationType();
        // 方法描述
        String methodDescription = annotation.description();

        boolean isAllowInfoLog = false;
        if (logClassNameList.contains(className) || logMethodNameList.contains(methodName) || isAllInfoLog
                || OperationType.WRITE.equals(operationEnum) || Environment.isDevEnv()) {
            isAllowInfoLog = true;
        }

        //打印日志 入参
        Object[] args = joinPoint.getArgs();
        if (isAllowInfoLog) {
            logger.info("进入{}.{} 方法, 入参: ", className, methodName);
            for (int i = 0; i < args.length; i++) {
                logger.info("参数{}：{} ", i, JsonUtils.toJson(args[i]));
            }
        }

        Transaction transaction = Cat.newTransaction(className, methodName);
        Object result = null;
        try {
            // 执行业务操作
            result = joinPoint.proceed();
            transaction.setSuccessStatus();
        } catch (Throwable throwable) {
            transaction.addData(CatUtils.buildStackInfo(methodDescription + "异常", throwable));
            logger.error("操作失败: {}: {}", methodDescription + "异常", throwable);
            transaction.setStatus(throwable);
            if (ServiceType.RPC_SERVICE.equals(logServiceEnum)) {
                result = RemoteResponse.custom().setFailure(throwable.getMessage()).build();
            }else if(ServiceType.COMMON_SERVICE.equals(logServiceEnum)){
                throw throwable;
            }
        } finally {
            transaction.complete();
        }
        if (isAllowInfoLog) {
            logger.info("结束{}.{} 方法,\n  出参: {} ", className, methodName, JsonUtils.toJson(result));
        }
        return result;
    }
}
