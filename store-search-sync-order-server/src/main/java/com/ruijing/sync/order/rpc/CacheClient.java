package com.ruijing.sync.order.rpc;

import com.ruijing.fundamental.cache.api.CacheKey;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.fundamental.remoting.msharp.constant.ProtocolConstant;
import com.ruijing.sync.order.log.annotation.ServiceLog;
import com.ruijing.sync.order.log.enums.ServiceType;

import java.util.Objects;
import java.util.Set;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2022/8/11 18:08
 */
@ServiceClient
public class CacheClient {

    private static final String SERVICE_NAME = "store-order";

    private static final String CAT_TYPE = "CacheClient";

    @MSharpReference(remoteAppkey = "msharp-cache-service", protocol = ProtocolConstant.REDIS)
    private com.ruijing.fundamental.cache.redis.client.RedisCacheClient redisCacheClient;

    @ServiceLog(description = "操作完成后立马删除对应的操作缓存，避免多余的等待时间", serviceType = ServiceType.COMMON_SERVICE)
    public void removeCache(String uniqKey) {
        Preconditions.notNull(uniqKey, "删除对应的操作缓存，key不可为null");
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        redisCacheClient.delete(cacheKey);
    }

    /**
     * @description: 缓存数据到redis, 如果返回null说明报错，否则为成功放入redis（true）或者已存在key（false）
     * @date: 2021/2/25 12:59
     * @author: zengyanru
     * @param uniqKey
     * @param value
     * @param timeLimit
     * @return void
     */
    @ServiceLog(description = "缓存数据到redis", serviceType = ServiceType.COMMON_SERVICE)
    public <T> Boolean putToCache(String uniqKey, T value, Integer timeLimit) {
        Preconditions.notNull(uniqKey, "缓存数据到redis，key不可为null");
        if (uniqKey.length() > 128) {
            return false;
        }
        try {
            CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
            Boolean putSuccess = redisCacheClient.set(cacheKey, value, timeLimit);
            return Objects.equals(putSuccess, true);
        } catch (Exception e) {
            // 不抛出异常，因为需要根据返回控制业务逻辑
            Cat.logError(CAT_TYPE, "putToCache", e.getMessage(), e);
            return false;
        } catch (Throwable e) {
            // 不抛出错误
            Cat.logError(CAT_TYPE, "putToCache", e.getMessage() + "一般是值内容过大导致的错误，可忽略", e);
            return false;
        }
    }

    /**
     * @description: 从缓存获取对象
     * @date: 2021/2/25 13:05
     * @author: zengyanru
     * @param uniqKey
     * @return java.lang.Object
     */
    @ServiceLog(description = "从缓存获取对象", serviceType = ServiceType.COMMON_SERVICE)
    public <T> T getFromCache(String uniqKey) {
        Preconditions.notNull(uniqKey, "从缓存获取对象，key不可为null");
        if (128 < uniqKey.length()) {
            return null;
        }
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        try {
            return redisCacheClient.get(cacheKey);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "getFromCache", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将set放入到cachekey中，若存在则添加，若不存在则创建
     * @param uniqKey
     * @param inputSet
     * @param <T>
     * @return
     */
    public <T> boolean setOrAdd(String uniqKey, Set<T> inputSet) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        redisCacheClient.sadd(cacheKey, inputSet.toArray());
        return true;
    }

    /**
     * 通过key获取set的成员
     * @param uniqKey
     * @return
     */
    public Set<Object> getSetMembers(String uniqKey) {
        return redisCacheClient.smembers(new CacheKey(SERVICE_NAME, uniqKey));
    }

    /**
     * 查询第一个入参的key中的set与其他key中的set的元素不同值，并且返回这些不同值的set，即第一个与后面的差集
     * @param keys key的数组（注意不是value的列表，是key里面还存了value）
     * @return
     */
    public <T> Set<T> sdiff(String... keys) {
        CacheKey[] cacheKeys = new CacheKey[keys.length];
        for (int i = 0; i < keys.length; i++) {
            cacheKeys[i] = new CacheKey(SERVICE_NAME, keys[i]);
        }
        return redisCacheClient.sdiff(cacheKeys);
    }
}
