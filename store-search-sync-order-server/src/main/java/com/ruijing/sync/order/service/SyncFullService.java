package com.ruijing.sync.order.service;

import com.google.common.collect.Lists;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.whitehole.database.dto.address.data.DeliveryOperationLogDTO;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.search.curd.request.BulkRequest;
import com.ruijing.search.curd.request.UpdateItemRequest;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.enums.DeliveryOperationEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.api.base.other.dto.OrderConfirmForTheRecordDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.sync.common.dto.SyncDataRequestDTO;
import com.ruijing.sync.order.client.SearchAPIClient;
import com.ruijing.sync.order.constant.OrderConstant;
import com.ruijing.sync.order.rpc.*;
import com.ruijing.sync.order.utils.CacheUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@CatAnnotation
public class SyncFullService {

    @Resource
    private OrderMasterRPCClient orderMasterRPCClient;

    @Resource
    private UserRPCClient userRPCClient;

    @Resource
    private SearchAPIClient searchAPIClient;

    @Resource
    private PurchaseApplicationRPCClient purchaseApplicationRPCClient;

    @Resource
    private OrderDetailRPCClient orderDetailRPCClient;

    @Resource
    private OrderGalaxyRpcClient orderGalaxyRpcClient;

    @Resource
    private OrderWhiteHoleClient orderWhiteHoleClient;

    @Resource
    private CacheClient cacheClient;

    @PearlValue(key = "PRINT_TO_CAT", defaultValue = "true")
    private Boolean printLogToCat;

    private static final String CAT_TYPE = "SyncFullService";


    private final Logger logger = LoggerFactory.getLogger(getClass());

    public Integer syncFullData(SyncDataRequestDTO params) {
        Integer lastFewDays = params.getLastFewDays();
        Integer lastHours = params.getLastHours();
        List<String> orderNoList = params.getNumberList();
        Integer lastMinutes = params.getLastMinutes();
        BulkRequest request = new BulkRequest();
        request.setKey(OrderConstant.FULL_KEY);
        int total = 0;

        // 手动同步最近 lastHours 小时的数据
        if (lastHours != null && lastHours > 0) {
            ZonedDateTime zonedDateTime = LocalDateTime.now().plusHours(-(lastHours)).atZone(ZoneId.systemDefault());
            total = this.updateAsyncOrderData(request, zonedDateTime);
        } else if (lastFewDays != null && lastFewDays > 0) {
            // 手动同步最近 lastFewDays 天的数据
            ZonedDateTime zonedDateTime = LocalDate.now().plusDays(-(lastFewDays)).atStartOfDay().atZone(ZoneId.systemDefault());
            total = this.updateAsyncOrderData(request, zonedDateTime);
        } else if (lastFewDays != null && lastFewDays == -1L) {
            // 完全全量做避免覆盖处理
            synchronized (this) {
                if (OrderConstant.synchronizingFlag == true) {
                    Preconditions.isTrue(false, "正在处理其他全量同步请求，请稍等");
                } else {
                    OrderConstant.synchronizingFlag = true;
                }
                OrderConstant.increRedisFlag = true;
            }
            // get order total
            Integer maxId = orderMasterRPCClient.findMaxId();
            int max = maxId.intValue();
            total = max;
            AsyncExecutor.listenableRunAsync(() -> {
                int count = 1;
                // 每次从数据库取500条数据
                while (count < max) {
                    logger.warn("当前全量同步进度{}/{}", count, max);
                    // init bulk request
                    BulkRequest requestAsync = new BulkRequest();
                    requestAsync.setKey(OrderConstant.FULL_KEY);
                    List<OrderMasterDTO> updatedOrderListAsync = orderMasterRPCClient.rangeById(count, count += 500);
                    // fetchOrderMasterData，选用此时没有增量同步的数据
                    boolean getLock = false;
                    try {
                        getLock = OrderConstant.reentrantLock.tryLock(10, TimeUnit.SECONDS);
                        if (!getLock) {
                            throw new IllegalStateException("增量同步出现卡顿，或者锁使用失败，请排查死锁和检查代码！");
                        }
                        // 全量过程中发生的增量数据不再同步
                        Set<Integer> orderIdSet = updatedOrderListAsync.stream().map(OrderMasterDTO::getId).collect(Collectors.toSet());
                        cacheClient.setOrAdd(OrderConstant.FULL_CACHE_KEY, orderIdSet);
                        Set<Integer> difOrderIdSet = cacheClient.sdiff(OrderConstant.FULL_CACHE_KEY, OrderConstant.ADDI_CACHE_KEY);

                        List<OrderMasterDTO> difMasterList = New.list();
                        for (OrderMasterDTO master : updatedOrderListAsync) {
                            if (difOrderIdSet.contains(master.getId())) {
                                difMasterList.add(master);
                            }
                        }
                        BulkUpdateOrderToSearch(requestAsync, difMasterList);
                    } catch (Exception e) {
                        logger.error("批量同步出错：",e);
                    } finally {
                        // 解锁和删除本批次的全量cache
                        if (getLock) {
                            OrderConstant.reentrantLock.unlock();
                        }
                        cacheClient.removeCache(OrderConstant.FULL_CACHE_KEY);
                    }
                }
                OrderConstant.synchronizingFlag = false;
                OrderConstant.increRedisFlag = false;
                cacheClient.removeCache(OrderConstant.ADDI_CACHE_KEY);
            }).addFailureCallback(throwable -> {
                final Transaction transaction = Cat.newTransaction(CAT_TYPE, "syncFullData");
                transaction.addData("全量同步订单数据失败:" + throwable.getMessage());
                transaction.setStatus(throwable);
                logger.error("全量同步订单数据失败:" + throwable);
                transaction.complete();
            });
        } else if (CollectionUtils.isNotEmpty(orderNoList)) {
            // 手动同步单号为orderNoList的数据(会存在全量覆盖增量的问题)
            List<OrderMasterDTO> updatedOrderList = orderMasterRPCClient.findByOrderOrderNoList(orderNoList);
            total = updatedOrderList.size();
            // fetchOrderMasterData
            BulkUpdateOrderToSearch(request, updatedOrderList);
        } else if (lastMinutes != null && lastMinutes > 0) {
            ZonedDateTime zonedDateTime = LocalDateTime.now().plusMinutes(-(lastMinutes)).atZone(ZoneId.systemDefault());
            total = this.updateAsyncOrderData(request, zonedDateTime);
        }

        logger.warn("完成数据同步，同步条数：{}", total);
        return total;
    }

    /**
     * 更新MySQL的订单数据到搜索，并返回更新数
     * @param request           更新请求入参
     * @param zonedDateTime     最后更新时间
     * @return                  更新条数
     */
    private int updateAsyncOrderData(BulkRequest request, ZonedDateTime zonedDateTime) {
        int total;
        List<Integer> lastUpdatedId = orderMasterRPCClient.findIdByUpdateTimeAfter(Date.from(zonedDateTime.toInstant()));
        total = lastUpdatedId.size();
        AsyncExecutor.listenableRunAsync(() -> {
            List<List<Integer>> lastUpdatedIdPartition = Lists.partition(lastUpdatedId, 500);
            for (int i = 0; i < lastUpdatedIdPartition.size(); i++) {
                List<Integer> updatedIdList = lastUpdatedIdPartition.get(i);
                List<OrderMasterDTO> updatedOrderList = orderMasterRPCClient.findByOrderIdSet(new HashSet<>(updatedIdList));
                this.BulkUpdateOrderToSearch(request, updatedOrderList);
                logger.warn("同步数据进度：{} %,末尾id{}", ((i + 1d)  / lastUpdatedIdPartition.size()) * 100d ,updatedIdList.get(updatedIdList.size()-1));
            }
        }).addFailureCallback(throwable -> {
            logger.error("手同步{}之后的订单数据失败:" + throwable, zonedDateTime.toLocalDateTime());
            Cat.logError(CAT_TYPE, "syncFullData", "手动同步" + zonedDateTime.toLocalDateTime() + "之后的订单数据失败", throwable);
        });
        return total;
    }

    public void BulkUpdateOrderToSearch(BulkRequest request, List<OrderMasterDTO> updatedOrderList) {
        String methodName = "BulkUpdateOrderToSearch";
        final Transaction transaction = Cat.newTransaction(CAT_TYPE, "BulkUpdateOrderToSearch");
        try {
            // fetchOrderMasterData
            this.fetchOrderMasterData(updatedOrderList, request);
            // bulk update data
            searchAPIClient.bulkUpdate(request);
            transaction.setSuccess();
        } catch (Exception e) {
            if (printLogToCat) {
                transaction.addData("批量更新出错，涉及id为：" + request.getRequests().stream().map(UpdateItemRequest::getId).collect(Collectors.joining(",")));
                transaction.setStatus(e);
            }
            logger.error("批量同步出错：",e);
        } finally {
            CacheUtils.removeCache(methodName);
            transaction.complete();
        }
    }

    /**
     * 订单insert 取数据组装
     * @param partition
     * @return
     */
    public void fetchOrderMasterData(List<OrderMasterDTO> partition, BulkRequest request) {
        List<Integer> orderIdList = partition.stream().map(o ->  o.getId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            // ignore
            return;
        }
        List<Long> buyappIdList = partition.stream().filter(o -> Objects.nonNull(o.getFtbuyappid())).map(o -> o.getFtbuyappid().longValue()).distinct().collect(Collectors.toList());
        List<Integer> departmentIdList = partition.stream().filter(o -> Objects.nonNull(o.getFbuydepartmentid())).map(o -> o.getFbuydepartmentid()).distinct().collect(Collectors.toList());
        List<String> orderNoList = partition.stream().map(o -> o.getForderno()).distinct().collect(Collectors.toList());
        // 反查order_detail
        List<OrderDetailDTO> orderDetailDTOList = orderDetailRPCClient.findByOrderIdList(orderIdList);
        Map<Integer, List<OrderDetailDTO>> orderIdDetailListMap = orderDetailDTOList.stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
        // 反查buyapplication
        List<ApplicationMasterDTO> applicationMasterDTOList = purchaseApplicationRPCClient.findApplicationMaster(buyappIdList);
        Map<Integer, ApplicationMasterDTO> idApplicationMap = applicationMasterDTOList.stream().collect(Collectors.toMap(item -> item.getId().intValue(), Function.identity(), (o, n) -> n));
        // 反查department
        List<DepartmentDTO> departmentDTOList = userRPCClient.findDepartmentByIdList(departmentIdList);
        Map<Integer, Integer> idParentDepartmentIdMap = new HashMap<>();
        for (DepartmentDTO dept : departmentDTOList) {
            idParentDepartmentIdMap.put(dept.getId(), dept.getParentId());
        }
        // 反查parent department
        List<DepartmentDTO> parentDepartmentIdList = new ArrayList<>(departmentDTOList.size());
        if (CollectionUtils.isNotEmpty(departmentDTOList)) {
            List<Integer> parentIdList = departmentDTOList.stream().map(DepartmentDTO::getParentId).filter(Objects::nonNull).collect(Collectors.toList());
            parentDepartmentIdList = userRPCClient.findDepartmentByIdList(parentIdList);
        }
        Map<Integer, DepartmentDTO> idParentDepartmentIdentityMap = parentDepartmentIdList.stream().collect(Collectors.toMap(DepartmentDTO::getId, Function.identity(), (o, n) -> n));
        // 反查dockingExtra
        List<DockingExtraDTO> dockingDTOList = orderMasterRPCClient.findDockingListByOrderNo(orderNoList);
        Map<String, List<DockingExtraDTO>> orderNoExtraInfoMap = dockingDTOList.stream().collect(Collectors.groupingBy(DockingExtraDTO::getInfo));
        // 反查orderApprovalLog
        List<OrderApprovalLogDTO> orderApprovalLogDTOList = orderMasterRPCClient.findApprovalLogByOrderIdList(orderIdList);
        Map<Integer, List<OrderApprovalLogDTO>> orderIdApprovalLogMap = orderApprovalLogDTOList.stream().collect(Collectors.groupingBy(OrderApprovalLogDTO::getOrderId));
        // 反查t_order_confirm_for_the_record
        List<OrderConfirmForTheRecordDTO> confirmByOrderIdList = orderMasterRPCClient.findOrderConfirmByOrderIdList(orderIdList);
        Map<Integer, OrderConfirmForTheRecordDTO> orderIdConfirmMap = confirmByOrderIdList.stream().collect(Collectors.toMap(OrderConfirmForTheRecordDTO::getOrderId, Function.identity(), (o, n) -> n));
        // 反查refFundCardOrder
        List<RefFundcardOrderDTO> orderFundCardList = orderMasterRPCClient.findCardByOrderIdList(orderIdList);
        Map<String, List<RefFundcardOrderDTO>> orderIdFundCardMap = orderFundCardList.stream().collect(Collectors.groupingBy(RefFundcardOrderDTO::getOrderId));
        // 反查order extra
        List<OrderExtraDTO> orderExtraList = orderMasterRPCClient.findOrderExtraByOrderId(orderIdList);
        Map<Integer, List<OrderExtraDTO>> orderIdExtraListMap = orderExtraList.stream().filter(orderExtraDTO -> {
            OrderExtraEnum orderExtraEnum = OrderExtraEnum.getByValue(orderExtraDTO.getExtraKey());
            // 空的话，就是这个项目没重新发版，这时候当成需要同步。如果取到了就根据那个值判断
            return orderExtraEnum == null || orderExtraEnum.getNeedSyncToSearch();
        }).collect(Collectors.groupingBy(OrderExtraDTO::getOrderId));
        // 反查地址代配送
        List<OrderAddressDTO> orderAddressList = orderGalaxyRpcClient.findByOrderId(orderIdList);
        Map<Integer, OrderAddressDTO> orderIdDeliveryProxyTypeMap = orderAddressList.stream().filter(Objects::nonNull).collect(Collectors.toMap(OrderAddressDTO::getId, Function.identity(), (ov, nv) -> nv));
        // 反查代配送日志表
        Map<Integer, List<DeliveryOperationLogDTO>> deliveryOperationLogMap = orderWhiteHoleClient.listOperationLogInOrderId(orderIdList).stream().collect(Collectors.groupingBy(DeliveryOperationLogDTO::getOrderId));

        // 解析
        List<Map<String, Object>> partitionList = ElasticSearchTranslator.parseOrderMasterDTO(partition);
        partitionList.stream().forEach(dmlData -> {
            dmlData.put("fbuyapplicationno", idApplicationMap.get(dmlData.get("ftbuyappid")) != null ? idApplicationMap.get(dmlData.get("ftbuyappid")).getApplyNumber() : StringUtils.EMPTY);
            dmlData.put("relateInfo", idApplicationMap.get(dmlData.get("ftbuyappid")) != null ? idApplicationMap.get(dmlData.get("ftbuyappid")).getRelatedInfo() : StringUtils.EMPTY);
            dmlData.put("department_parent_name", idParentDepartmentIdentityMap.get(idParentDepartmentIdMap.get(dmlData.get("fbuydepartmentid"))) != null ? idParentDepartmentIdentityMap.get(idParentDepartmentIdMap.get(dmlData.get("fbuydepartmentid"))).getName() : StringUtils.EMPTY);
            dmlData.put("department_parent_id", idParentDepartmentIdMap.get(dmlData.get("fbuydepartmentid")) != null ? idParentDepartmentIdMap.get(dmlData.get("fbuydepartmentid")) : StringUtils.EMPTY);
            List<DockingExtraDTO> dockingExtraDTOList = orderNoExtraInfoMap.get(dmlData.get("forderno"));
            if (CollectionUtils.isNotEmpty(dockingExtraDTOList)) {
                for (DockingExtraDTO extraDTO : dockingExtraDTOList) {
                    if (OrderConstant.DOCKING_TYPE.equals(extraDTO.getType())) {
                        dmlData.put("extra_info", extraDTO.getExtraInfo());
                        dmlData.put("docking_status", extraDTO.getStatusextra());
                    } else if (OrderConstant.DOCKING_STATEMENT_TYPE.equals(extraDTO.getType())) {
                        dmlData.put("extra_statement", extraDTO.getExtraInfo());
                    }
                }
            }

            List<OrderApprovalLogDTO> approvalLogItemList = orderIdApprovalLogMap.get(dmlData.get("id"));
            List<Map<String, Object>> approvalLogMap = ElasticSearchTranslator.parseApprovalLogField(approvalLogItemList);
            dmlData.put("log", approvalLogMap);
            OrderConfirmForTheRecordDTO confirm = orderIdConfirmMap.get(dmlData.get("id"));
            if (confirm != null) {
                dmlData.put("is_confirm", confirm.getConfirm());
                dmlData.put("confirm_type", confirm.getType());
            }

            List<OrderDetailDTO> orderDetailItemList = orderIdDetailListMap.get(dmlData.get("id"));
            if (CollectionUtils.isNotEmpty(orderDetailItemList)) {
                List<Map<String, Object>> orderDetailDataMap = ElasticSearchTranslator.parseOrderDetailField(orderDetailItemList);
                dmlData.put("order_detail", orderDetailDataMap);
            }
            List<OrderExtraDTO> orderExtraItemList = orderIdExtraListMap.get(dmlData.get("id"));
            if (CollectionUtils.isEmpty(orderExtraItemList)) {
                dmlData.put("order_extra", New.list());
                dmlData.put("customer_subscribe_supp", "0");
                dmlData.put("regular_customer_purchase", "0");
            } else if (CollectionUtils.isNotEmpty(orderExtraItemList)) {
                List<Map<String, Object>> orderExtraDataMap = ElasticSearchTranslator.parseOrderExtraField(orderExtraItemList);
                dmlData.put("order_extra", orderExtraDataMap);
                Map<Object, Object> extraKeyValMap = orderExtraDataMap.stream().collect(Collectors.toMap(m->m.get("extra_key"), m->m.get("extra_value"), (o, n)->n));
                dmlData.put("risk_verified_status", extraKeyValMap.get(OrderExtraEnum.RISK_VERIFIED_STATUS.getValue()));
                dmlData.put("customer_subscribe_supp", extraKeyValMap.getOrDefault(OrderExtraEnum.CUSTOMER_SUBSCRIBE_SUPP.getValue(), "0"));
                dmlData.put("regular_customer_purchase", extraKeyValMap.getOrDefault(OrderExtraEnum.REGULAR_CUSTOMER_PURCHASE.getValue(), "0"));
            }
            // 代配送
            OrderAddressDTO orderAddressDTO = orderIdDeliveryProxyTypeMap.get(dmlData.get("id"));
            if(orderAddressDTO != null) {
                Integer deliveryProxyType = orderAddressDTO.getDeliveryType();
                dmlData.put("delivery_proxy",DeliveryTypeEnum.PROXY_DELIVERY_LIST.contains(deliveryProxyType));
                dmlData.put("delivery_status", orderAddressDTO.getDeliveryStatus());
                dmlData.put("delivery_user", orderAddressDTO.getDeliveryUser());
                dmlData.put("deliveryType", deliveryProxyType);
                dmlData.put("sorted_user", orderAddressDTO.getSortedUser());
                dmlData.put("province", orderAddressDTO.getProvince());
            }
            List<DeliveryOperationLogDTO> deliveryOperationLogDTOList = deliveryOperationLogMap.get(dmlData.get("id"));
            if(CollectionUtils.isNotEmpty(deliveryOperationLogDTOList)) {
                List<String> deliveryOperatorGuids = New.list();
                deliveryOperationLogDTOList.forEach(deliveryOperationLogDTO -> {
                    deliveryOperatorGuids.add(deliveryOperationLogDTO.getOperatorGuid());
                    if (DeliveryOperationEnum.SORTED.getValue().equals(deliveryOperationLogDTO.getOperateType())){
                        dmlData.put("delivery_sorted_time", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, deliveryOperationLogDTO.getOperateDate()));
                    }else if (DeliveryOperationEnum.DELIVERED.getValue().equals(deliveryOperationLogDTO.getOperateType())){
                        dmlData.put("delivered_time", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, deliveryOperationLogDTO.getOperateDate()));
                    }
                });
                dmlData.put("delivery_operator_guids", deliveryOperatorGuids);
            }

            List<RefFundcardOrderDTO> refFundcardOrderItemList = orderIdFundCardMap.get(dmlData.get("id") != null ? dmlData.get("id").toString() : "-1");
            if (CollectionUtils.isNotEmpty(refFundcardOrderItemList)) {
                List<Map<String, Object>> orderFundCardDataMap = ElasticSearchTranslator.parseOrderCardField(refFundcardOrderItemList);
                dmlData.put("card", orderFundCardDataMap);
            }

            request.addUpdateRequest(dmlData.get("id") != null ? dmlData.get("id").toString() : "-1", dmlData);
        });

    }
}
