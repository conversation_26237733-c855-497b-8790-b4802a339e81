package com.ruijing.sync.order.client;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.local.deploy.api.order.dto.SyncOrderDetailMessageDTO;
import com.reagent.local.deploy.api.order.dto.SyncOrderLogMessageDTO;
import com.reagent.local.deploy.api.order.dto.SyncOrderMessageDTO;
import com.reagent.local.deploy.api.order.enums.EventTypeEnum;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.ruijing.base.mq.rpc.MqRpcService;
import com.ruijing.data.sync.api.annotation.DataListener;
import com.ruijing.data.sync.api.enums.ClusterTypeEnum;
import com.ruijing.data.sync.api.enums.EventType;
import com.ruijing.data.sync.api.event.DataEvent;
import com.ruijing.data.sync.api.listener.DataChangedListener;
import com.ruijing.data.sync.api.model.DmlData;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.search.curd.request.BulkRequest;
import com.ruijing.search.curd.request.DeleteItemRequest;
import com.ruijing.search.curd.request.UpdateItemRequest;
import com.ruijing.search.curd.script.UpdateScript;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.enums.DeliveryOperationEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterCommonReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.api.base.other.dto.OrderRemarkDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.sync.order.constant.OrderConstant;
import com.ruijing.sync.order.constant.OrganizationConstant;
import com.ruijing.sync.order.rpc.*;
import com.ruijing.sync.order.service.ElasticSearchTranslator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 基础同步客户端
 * @author: zhongyulei
 * @create: 2020/10/13 17:24
 **/
@CatAnnotation
@DataListener(key = "order", supportedClusterType = ClusterTypeEnum.KAFKA)
public class BaseDataSyncClient implements DataChangedListener {
    private static final String ORDER_MASTER                 = "torder_master";
    private static final String ORDER_DETAIL                 = "torder_detail";
    private static final String REF_FUND_CARD_ORDER          = "t_ref_fundcard_order";
    private static final String BUY_APPLICATION_MASTER       = "tbuyapplication_master";
    private static final String DANGEROUS_TAG                = "t_dangerous_tag";
    private static final String DEPARTMENT                   = "t_department";
    private static final String DOCKING_EXTRA                = "t_docking_extra";
    private static final String OFFLINE_DANGEROUS_TAG        = "t_offline_dangerous_tag";
    private static final String ORDER_APPROVAL_LOG           = "t_order_approval_log";
    private static final String ORDER_CONFIRM_FOR_THE_RECORD = "t_order_confirm_for_the_record";
    private static final String DELIVERY_OPERATION_LOG       = "delivery_operation_log";
    private static final String STATEMENT                    = "t_statement";
    private static final String SUMMARY                      = "t_summary";
    private static final String ORDER_EXTRA                  = "order_extra";
    private static final String ORDER_ADDRESS                = "order_address";


    public static final String CAT_TYPE = "BaseDataSyncClient";

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseDataSyncClient.class);

    @MSharpReference(remoteAppkey = "base-biz-mq-service")
    private MqRpcService<SyncOrderMessageDTO> mqRpcService;

    @Resource
    private OrderMasterRPCClient orderMasterRPCClient;

    @Resource
    private UserRPCClient userRPCClient;

    @Resource
    private SearchAPIClient searchAPIClient;

    @Resource
    private PurchaseApplicationRPCClient purchaseApplicationRPCClient;

    @Resource
    private OrderDetailRPCClient orderDetailRPCClient;

    @Resource
    private OrderGalaxyRpcClient orderGalaxyRpcClient;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private OrderWhiteHoleClient orderWhiteHoleClient;

    @Override
    public void dataChanged(DataEvent dataEvent) {
        List<DmlData> dmls = dataEvent.getDataList();

        if (CollectionUtils.isEmpty(dmls)) {
            return;
        }
        int i = 0;
        AsyncExecutor.runAsync(() -> {
            // 任何数据先记录到日志
            LOGGER.info("curDataChanged: {}", dmls);
        });

        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.setKey(OrderConstant.CANAL_KEY);
        for (int size = dmls.size(); i < size; ++i) {
            DmlData dml = dmls.get(i);
            // 封装es数据
            try {
                bulkSaveDmlData(dml, bulkRequest);
            } catch (Exception e) {
                LOGGER.error("同步订单相关数据异常，dml=" + dml, e);
                Cat.logError(CAT_TYPE, "dataChanged", "同步订单相关数据异常，dml=" + dml, e);
            }
            // 异步推送数据到本地部署
            asyncPushLocalDeployOrderData(dml);
        }
        // 推送增量数据到elasticsearch
        searchAPIClient.bulkUpdate(bulkRequest);
    }

    private void asyncPushLocalDeployOrderData(DmlData dml) {
        AsyncExecutor.runAsync(() -> {
            try {
                pushLocalDeployOrderData(dml);
            } catch (Exception e) {
                Cat.logError("BaseDataSyncClient", "dataChanged", "推送订单数据到本地部署失败！", e);
            }
        });
    }

    /**
     * 批量保存/更新数据
     * @param dml
     * @param bulkRequest
     */
    private void bulkSaveDmlData(DmlData dml, BulkRequest bulkRequest) {
        List<Map<String, Object>> dataList = dml.getData();
        EventType eventType = dml.getEventType();
        String tableName = dml.getTableName();
        // 删除nested表时候的处理
        if (EventType.DELETE.equals(eventType)) {
            if (ORDER_DETAIL.equals(tableName)) {
                // 写脚本来处理
                String script = "ctx._source.order_detail.removeIf(item -> params.detail_id.contains(item.detail_id))";
                for (Map<String, Object> dataMap : dataList) {
                    String masterId = Optional.ofNullable(dataMap.get("fmasterid")).map(Object::toString).orElse("-1");
                    Integer detailId = Integer.valueOf(Optional.ofNullable(dataMap.get("id")).map(Object::toString).orElse("-1"));
                    if ("-1".equals(masterId)) {
                        continue;
                    }
                    Map<String, Object> detailIdMap = new HashMap<>();
                    detailIdMap.put("detail_id", New.list(detailId));

                    UpdateItemRequest updateItemRequest = new UpdateItemRequest();
                    updateItemRequest.setId(masterId);
                    updateItemRequest.setUpdateScript(new UpdateScript(script, detailIdMap));
                    bulkRequest.addUpdateRequest(updateItemRequest);
                }
                return;
            }
            if (ORDER_EXTRA.equalsIgnoreCase(tableName)) {
                // 写脚本来处理
                String script = "ctx._source.order_extra.removeIf(item -> params.extra_id.contains(item.extra_id))";
                for (Map<String, Object> dataMap : dataList) {
                    String masterId = Optional.ofNullable(dataMap.get("order_id")).map(Object::toString).orElse("-1");
                    Integer extraId = Integer.valueOf(Optional.ofNullable(dataMap.get("id")).map(Object::toString).orElse("-1"));
                    if ("-1".equals(masterId)) {
                        continue;
                    }
                    Map<String, Object> extraIdMap = new HashMap<>();
                    extraIdMap.put("extra_id", New.list(extraId));

                    UpdateItemRequest updateItemRequest = new UpdateItemRequest();
                    updateItemRequest.setId(masterId);
                    updateItemRequest.setUpdateScript(new UpdateScript(script, extraIdMap));
                    bulkRequest.addUpdateRequest(updateItemRequest);
                }
                return;
            }
        }

        // 只要是从表的推送，都需要反查订单主表的数据组装数据
        switch (tableName) {
            case ORDER_MASTER:
                // TODO:对表进行分类处理，如果是master表就处理，其他表暂不处理，因为全量只有对master表的处理；而且后面会有增量数据补偿，会在一个小时内掩盖这里的漏洞
                //  （其他表的更新也可能被全量覆盖的bug）
                boolean getLock = false;
                Set<Integer> orderIdSet = new HashSet<>();
                try {
                    getLock = OrderConstant.reentrantLock.tryLock(100, TimeUnit.SECONDS);
                    if (!getLock) {
                        throw new IllegalStateException("全量同步太慢，或者有锁未释放，请检查代码！");
                    }
                    dataList.forEach(datum -> {
                        Integer orderId = (Integer) datum.get("id");
                        orderIdSet.add(orderId);
                    });
                    if (OrderConstant.increRedisFlag) {
                        // 侵入，若为全量同步过程，需要将orderId放入缓存；
                        cacheClient.setOrAdd(OrderConstant.ADDI_CACHE_KEY, orderIdSet);
                    }
                    if (OrderConstant.compensationIncreRedisFlag) {
                        // 侵入，若为全量补偿过程，需要将orderId放入缓存；
                        cacheClient.setOrAdd(OrderConstant.ADDI_COMPENSATE_KEY, orderIdSet);
                    }
                    // 正式同步
                    dataList = updatedOrderMasterDmlData(dataList, eventType);
                } catch (InterruptedException e) {
                    Cat.logError(CAT_TYPE, "bulkSaveDmlData", "下述masterid增量同步失败：" + orderIdSet, e);
                    LOGGER.error("下述masterid增量同步失败：" + orderIdSet);
                    return;
                } finally {
                    if (getLock) {
                        OrderConstant.reentrantLock.unlock();
                    }
                }
                break;
            case ORDER_DETAIL:
                dataList = updatedOrderDetailDmlData(dataList, eventType);
                break;
            case ORDER_EXTRA:
                dataList = updateOrderExtraDmlData(dataList, eventType);
                break;
            case ORDER_ADDRESS:
                dataList = updateOrderAddressDmlData(dataList, eventType);
                break;
            case REF_FUND_CARD_ORDER:
                dataList = updatedOrderFundCardDmlData(dataList);
                break;
            case BUY_APPLICATION_MASTER:
                dataList = updatedBuyApplicationDmlData(dataList);
                break;
            case DANGEROUS_TAG:
                // 快照信息ignore
                return;
            case DEPARTMENT:
                // 快照信息ignore
                return;
            case DOCKING_EXTRA:
                dataList = updatedDockingExtraDmlData(dataList);
                break;
            case OFFLINE_DANGEROUS_TAG:
                // 快照信息ignore
                return;
            case ORDER_APPROVAL_LOG:
                dataList = updatedOrderApprovalLogDmlData(dataList);
                break;
            case ORDER_CONFIRM_FOR_THE_RECORD:
                dataList = updatedOrderConfirmRecordDmlData(dataList);
                break;
            case DELIVERY_OPERATION_LOG:
                dataList = updatedDeliveryOperationLogDmlData(dataList);
                break;
            case STATEMENT:
                // parseOrderMasterDmlData 已经设置statementId，ignore
                return;
            case SUMMARY:
                // ignore
                return;
        }

        if (eventType == EventType.DELETE && ORDER_MASTER.equals(tableName)) {
            for (Map<String, Object> map : dataList) {
                bulkRequest.addDeleteIds(map.get("id").toString());
            }
        } else {
            for (Map<String, Object> dataMap : dataList) {
                String id = Optional.ofNullable(dataMap.get("id")).map(Object::toString).orElse("-1");
                Set<String> deleteIdSet = bulkRequest.getDeleteItemRequests().stream().map(DeleteItemRequest::getId).collect(Collectors.toSet());
                if (!deleteIdSet.contains(id)) {
                    bulkRequest.addUpdateRequest(id, dataMap);
                }
            }
        }
    }

    private List<Map<String, Object>> updatedDeliveryOperationLogDmlData(List<Map<String, Object>> dataList) {
        if(CollectionUtils.isEmpty(dataList)){
            return Collections.emptyList();
        }
        List<Map<String, Object>> resultList = New.list();
        Map<Integer, Map<String, Object>> orderIdDeliveryOperationLogMap = dataList.stream().filter(map -> map.get("order_id") != null).collect(Collectors.toMap(map -> (Integer) map.get("order_id"), Function.identity(), (o, n) -> n));
        orderIdDeliveryOperationLogMap.values().forEach(map ->{
            Map<String, Object> result = new HashMap<>();
            result.put("id", map.get("order_id"));
            if(DeliveryOperationEnum.SORTED.getValue().equals(map.get("operate_type"))){
                result.put("delivery_sorted_time", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, (Date) map.get("operate_date")));
            }else if(DeliveryOperationEnum.DELIVERED.getValue().equals(map.get("operate_type"))){
                result.put("delivered_time", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, (Date) map.get("operate_date")));
            }
            result.put("delivery_operator_guids", map.get("operator_guid"));
            resultList.add(result);
        });

        return resultList;
    }

    /**
     * 增量更新订单额外信息表
     * @param partition
     * @param eventType
     * @return
     */
    private List<Map<String, Object>> updateOrderExtraDmlData(List<Map<String, Object>> partition, EventType eventType) {
        if (eventType == EventType.DELETE) {
            // 无需封装nested的数据,TODO:需要用脚本删除nested的表；
            return Collections.emptyList();
        }
        // 因为bulk update只允许更新不允许列表添加，因此每次或添加extra info都需要查询全部extra信息并更新整个列表；
        List<Integer> orderIdList = New.list();
        partition.forEach(part -> {
            Integer orderId = (Integer) part.get("order_id");
            orderIdList.add(orderId);
        });
        // 反查order extra表
        List<OrderExtraDTO> extraList = orderMasterRPCClient.findOrderExtraByOrderId(orderIdList);
        Map<Integer, List<OrderExtraDTO>> orderIdExtraMap = extraList.stream().collect(Collectors.groupingBy(OrderExtraDTO::getOrderId));
  // 构造更新列表
        Map<Integer, List<Map<String, Object>>> orderIdExtraKeyValueMap = new HashMap<>();
        for (Map<String, Object> part : partition) {
            Integer orderId = (Integer) part.get("order_id");
            List<OrderExtraDTO> curExtraList = orderIdExtraMap.get(orderId);
            for (OrderExtraDTO extraDTO : curExtraList) {
                OrderExtraEnum orderExtraEnum;
                if((orderExtraEnum = OrderExtraEnum.getByValue(extraDTO.getExtraKey())) != null && !orderExtraEnum.getNeedSyncToSearch()){
                    // 跳过不需要同步的order_extra。空的话，就是这个项目没重新发版，这时候当成需要同步
                    continue;
                }
                Map<String, Object> extraInfoMap = new HashMap<>();
                extraInfoMap.put("extra_id", extraDTO.getId());
                extraInfoMap.put("extra_key", extraDTO.getExtraKey());
                extraInfoMap.put("extra_value", ElasticSearchTranslator.parseOrderExtraValue(extraDTO.getExtraKey(), extraDTO.getExtraValue()));
                if (orderIdExtraKeyValueMap.containsKey(orderId)) {
                    orderIdExtraKeyValueMap
                            .get(orderId)
                            .add(extraInfoMap);
                } else {
                    orderIdExtraKeyValueMap.put(orderId, New.list(extraInfoMap));
                }
            }
        }

        // 配置到es中
        Set<Integer> orderIdSet = partition.stream().map(map -> (Integer) map.get("order_id")).collect(Collectors.toSet());
        List<Map<String, Object>> resMapList = ElasticSearchTranslator.parseOrderMasterDTOSkip(orderIdSet);
        resMapList.forEach(master -> {
            List<Map<String, Object>> orderExtraNestedData = orderIdExtraKeyValueMap.get(master.get("id"));
            master.put("order_extra", orderExtraNestedData);
            // 额外增加 RISK_VERIFIED_STATUS-订单待核实 这个extraKey的数据到es的最外层
            if(CollectionUtils.isNotEmpty(orderExtraNestedData)){
                Map<Object, Object> extraKeyValMap = orderExtraNestedData.stream().collect(Collectors.toMap(m->m.get("extra_key"), m->m.get("extra_value"), (o, n)->n));
                master.put("risk_verified_status", extraKeyValMap.get(OrderExtraEnum.RISK_VERIFIED_STATUS.getValue()));
                master.put("customer_subscribe_supp", extraKeyValMap.getOrDefault(OrderExtraEnum.CUSTOMER_SUBSCRIBE_SUPP.getValue(), "0"));
                master.put("regular_customer_purchase", extraKeyValMap.getOrDefault(OrderExtraEnum.REGULAR_CUSTOMER_PURCHASE.getValue(), "0"));
            } else {
                master.put("customer_subscribe_supp", "0");
                master.put("regular_customer_purchase", "0");
            }
        });
        return resMapList;
    }

    /**
     * 更新订单代配送标识
     * @param partition
     * @param eventType
     * @return
     */
    private List<Map<String, Object>> updateOrderAddressDmlData(List<Map<String, Object>> partition, EventType eventType) {
        if (eventType == EventType.DELETE) {
            return Collections.emptyList();
        }
        List<Integer> orderIdList = New.list();
        partition.forEach(part -> {
            Integer orderId = (Integer) part.get("id");
            orderIdList.add(orderId);
        });
        // 反查地址表，获取是否代配送
        List<OrderAddressDTO> orderAddressList = orderGalaxyRpcClient.findByOrderId(orderIdList);
        Map<Integer, OrderAddressDTO> orderAddressDTOMap = orderAddressList.stream().collect(Collectors.toMap(OrderAddressDTO::getId, Function.identity()));

        // 配置到es中
        Set<Integer> orderIdSet = partition.stream().map(map -> (Integer) map.get("id")).collect(Collectors.toSet());
        List<Map<String, Object>> resMapList = ElasticSearchTranslator.parseOrderMasterDTOSkip(orderIdSet);
        resMapList.forEach(master -> {
            OrderAddressDTO orderAddressDTO = orderAddressDTOMap.get(master.get("id"));
            if(orderAddressDTO != null) {
                Integer deliveryProxyType = orderAddressDTO.getDeliveryType();
                master.put("delivery_proxy", DeliveryTypeEnum.PROXY_DELIVERY_LIST.contains(deliveryProxyType));
                master.put("delivery_status", orderAddressDTO.getDeliveryStatus());
                master.put("delivery_user", orderAddressDTO.getDeliveryUser());
                master.put("deliveryType", deliveryProxyType);
                master.put("sorted_user", orderAddressDTO.getSortedUser());
                master.put("province", orderAddressDTO.getProvince());
            }
        });
        return resMapList;
    }

    /**
     * 解析订单详情 DML 数据
     * @param partition
     */
    private List<Map<String, Object>> updatedOrderDetailDmlData(List<Map<String, Object>> partition, EventType eventType) {
        if (eventType == EventType.DELETE) {
            // 无需封装nested的数据,TODO:需要用脚本删除nested的表；
            return Collections.emptyList();
        }
        Set<Integer> masterIdSet = partition.stream().map(map -> (Integer) map.get("fmasterid")).collect(Collectors.toSet());
        List<Map<String, Object>> result = ElasticSearchTranslator.parseOrderMasterDTOSkip(masterIdSet);
        List<OrderDetailDTO> orderDetailList = orderDetailRPCClient.findByOrderIdList(new ArrayList<>(masterIdSet));
        Map<Integer, List<OrderDetailDTO>> orderIdDetailListMap = orderDetailList.stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
        result.forEach(master -> {
            List<OrderDetailDTO> orderDetailItem = orderIdDetailListMap.get(master.get("id"));
            master.put("order_detail", ElasticSearchTranslator.parseOrderDetailField(orderDetailItem));
        });

        return result;
    }

    /**
     * 解析订单经费卡 DML 数据
     * @param partition
     */
    private List<Map<String, Object>> updatedOrderFundCardDmlData(List<Map<String, Object>> partition) {
        Set<Integer> masterIdList = partition.stream().filter(f -> f.get("order_id") != null).map(map -> Integer.parseInt((String) map.get("order_id"))).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(masterIdList)) {
            return Collections.emptyList();
        }
        List<RefFundcardOrderDTO> cardByOrderIdList = orderMasterRPCClient.findCardByOrderIdList(new ArrayList<>(masterIdList));
        Map<String, List<RefFundcardOrderDTO>> orderIdFundCardListMap = cardByOrderIdList.stream().collect(Collectors.groupingBy(RefFundcardOrderDTO::getOrderId));

        List<Map<String, Object>> result = ElasticSearchTranslator.parseOrderMasterDTOSkip(masterIdList);
        result.forEach(master -> {
            List<RefFundcardOrderDTO> refFundcardOrderDTOList = orderIdFundCardListMap.get(master.get("id").toString());
            master.put("card", ElasticSearchTranslator.parseOrderCardField(refFundcardOrderDTOList));
        });
        return result;
    }

    /**
     * 解析订单对接单 DML 数据
     * @param partition
     */
    private List<Map<String, Object>> updatedDockingExtraDmlData(List<Map<String, Object>> partition) {
        List<String> orderNoList = partition.stream().map(map -> (String) map.get("info")).distinct().collect(Collectors.toList());
        List<DockingExtraDTO> dockingListByOrderNo = orderMasterRPCClient.findDockingListByOrderNo(orderNoList);
        // 订单号到docking extra的map
        Map<String, List<DockingExtraDTO>> orderNoExtraInfoMap = dockingListByOrderNo.stream().collect(Collectors.groupingBy(DockingExtraDTO::getInfo));

        List<OrderMasterDTO> byOrderNoSet = orderMasterRPCClient.findByOrderOrderNoList(orderNoList);
        if (CollectionUtils.isEmpty(byOrderNoSet)) {
            return Collections.emptyList();
        }
        // 订单id与docking extra信息的对应与拼装
        List<Map<String, Object>> result = New.list();
        for (OrderMasterDTO orderMasterDTO : byOrderNoSet) {
            List<DockingExtraDTO> dockingExtraDTOList = orderNoExtraInfoMap.get(orderMasterDTO.getForderno());
            if (CollectionUtils.isNotEmpty(dockingExtraDTOList)) {
                Map<String, Object> resPart = new HashMap<>();
                resPart.put("id", orderMasterDTO.getId());
                for (DockingExtraDTO dockingExtra : dockingExtraDTOList) {
                    if (OrderConstant.DOCKING_TYPE.equals(dockingExtra.getType())) {
                        resPart.put("extra_info", dockingExtra.getExtraInfo());
                        resPart.put("docking_status", dockingExtra.getStatusextra());
                        result.add(resPart);
                    } else if (OrderConstant.DOCKING_STATEMENT_TYPE.equals(dockingExtra.getType())) {
                        resPart.put("extra_statement", dockingExtra.getExtraInfo());
                        result.add(resPart);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 解析订单审批日志 DML 数据
     * @param partition
     */
    private List<Map<String, Object>> updatedOrderApprovalLogDmlData(List<Map<String, Object>> partition) {
        Integer ignoreOrderId = 3438839;
        Set<Integer> masterIdList = partition.stream().map(map -> (Integer) map.get("order_id"))
                .filter(orderId-> !ignoreOrderId.equals(orderId))
                .collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(masterIdList)){
            return New.list();
        }
        List<OrderApprovalLogDTO> approvalLogByOrderIdList = orderMasterRPCClient.findApprovalLogByOrderIdList(new ArrayList<>(masterIdList));
        Map<Integer, List<OrderApprovalLogDTO>> orderIdApprovalLogMap = approvalLogByOrderIdList.stream().collect(Collectors.groupingBy(OrderApprovalLogDTO::getOrderId));

        List<Map<String, Object>> result = ElasticSearchTranslator.parseOrderMasterDTOSkip(masterIdList);

        result.forEach(master -> {
            List<OrderApprovalLogDTO> orderApprovalLogDTOList = orderIdApprovalLogMap.get(master.get("id"));
            master.put("log", ElasticSearchTranslator.parseApprovalLogField(orderApprovalLogDTOList));
        });

        return result;
    }

    /**
     * 解析订单备案 DML 数据
     * @param partition
     */
    private List<Map<String, Object>> updatedOrderConfirmRecordDmlData(List<Map<String, Object>> partition) {
        Set<Integer> masterIdList = partition.stream().map(map -> (Integer) map.get("order_id")).collect(Collectors.toSet());
        Map<Integer, Map<String, Object>> orderIdConfirmRecordMap = partition.stream().collect(Collectors.toMap(map -> (Integer) map.get("order_id"), Function.identity(), (o, n) -> n));
        List<Map<String, Object>> result = ElasticSearchTranslator.parseOrderMasterDTOSkip(masterIdList);
        result.forEach(master -> {
            Map<String, Object> confirm = orderIdConfirmRecordMap.get(master.get("id"));
            if (confirm != null) {
                master.put("is_confirm", Byte.valueOf("1").equals(confirm.get("is_confirm")));
                master.put("confirm_type", confirm.get("type"));
            }
        });

        return result;
    }

    /**
     * 解析订单对应采购单 DML 数据
     * @param partition
     */
    private List<Map<String, Object>> updatedBuyApplicationDmlData(List<Map<String, Object>> partition) {
        List<Integer> buyerIdList = partition.stream().filter(o -> Objects.nonNull(o.get("id"))).map(map -> ((Integer) map.get("id"))).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(buyerIdList)) {
            return Collections.emptyList();
        }

        List<Long> buyerIdLongList = buyerIdList.stream().map(Integer::longValue).collect(Collectors.toList());
        List<ApplicationMasterDTO> applicationMaster = purchaseApplicationRPCClient.findApplicationMaster(buyerIdLongList);
        List<OrderMasterDTO> orderByApplicationIdList = orderMasterRPCClient.findOrderByApplicationIdList(buyerIdList);
        if (CollectionUtils.isEmpty(orderByApplicationIdList)) {
            return Collections.emptyList();
        }
        Set<Integer> orderIdSet = orderByApplicationIdList.stream().map(OrderMasterDTO::getId).collect(Collectors.toSet());
        List<Map<String, Object>> result = ElasticSearchTranslator.parseOrderMasterDTOSkip(orderIdSet);

        Map<Long, ApplicationMasterDTO> appIdIdentityMap = applicationMaster.stream().collect(Collectors.toMap(ApplicationMasterDTO::getId, Function.identity(), (o, n) -> n));
        result.forEach(master -> {
            Integer ftbuyappid = (Integer) master.get("ftbuyappid");
            ApplicationMasterDTO application = appIdIdentityMap.get(ftbuyappid.longValue());
            if (application != null) {
                master.put("fbuyapplicationno", application.getApplyNumber());
                master.put("relateInfo", application.getRelatedInfo());
            }
        });
        return result;
    }

    /**
     * 解析dml数据到Elasticsearch的主表数据
     * @param partition
     * @return
     */
    private List<Map<String, Object>> updatedOrderMasterDmlData(List<Map<String, Object>> partition, EventType eventType) {
        if (eventType == EventType.DELETE) {
            return partition;
        }
        List<Map<String, Object>> mapList = new ArrayList<>(partition.size());
        // 批量获取部门id，采购单id，订单no；
        List<Integer> deptIdList = New.list();
        List<Long> appIdList = New.list();
        List<String> orderNoList = New.list();
        for (Map<String, Object> part : partition) {
            Integer deptId = (Integer) part.get("fbuydepartmentid");
            Integer buyAppId = (Integer) part.get("ftbuyappid");
            String orderNo = (String) part.get("forderno");
            if (deptId != null) {
                deptIdList.add(deptId);
            }
            if (buyAppId != null) {
                Long buyAppIdLong = Long.valueOf(buyAppId);
                appIdList.add(buyAppIdLong);
            }
            if (orderNo != null) {
                orderNoList.add(orderNo);
            }
        }

        Map<Integer, Integer> deptIdToParentIdMap = new HashMap<>();
        Map<Integer, DepartmentDTO> parentIdDeptMap = new HashMap<>();
        Map<Long, ApplicationMasterDTO> appIdNoMap = new HashMap<>();
        Map<String, List<DockingExtraDTO>> orderNoDockingMap = new HashMap<>();
        // 批量找父级部门
        CompletableFuture<?> batchFindParentDeptFuture = AsyncExecutor.listenableRunAsync(() -> {
            List<DepartmentDTO> deptList = userRPCClient.findDepartmentByIdList(deptIdList);
            List<Integer> deptParentIdList = New.list();
            for (DepartmentDTO dept : deptList) {
                deptIdToParentIdMap.put(dept.getId(), dept.getParentId());
                deptParentIdList.add(dept.getParentId());
            }
            List<DepartmentDTO> deptParentList = userRPCClient.findDepartmentByIdList(deptParentIdList);
            for (DepartmentDTO parentDept : deptParentList) {
                parentIdDeptMap.put(parentDept.getId(), parentDept);
            }
        }).addFailureCallback(throwable -> {
            Cat.logError(CAT_TYPE, "updateOrderMasterDmlData", "批量获取父级单位失败", throwable);
        }).completable();


        // 批量找采购单
        CompletableFuture<?> batchFindApplyFuture = AsyncExecutor.listenableRunAsync(() -> {
            List<ApplicationMasterDTO> appMasterList = purchaseApplicationRPCClient.findApplicationMaster(appIdList);
            for (ApplicationMasterDTO appMaster : appMasterList) {
                appIdNoMap.put(appMaster.getId(), appMaster);
            }
        }).addFailureCallback(throwable -> {
            Cat.logError(CAT_TYPE, "updateOrderMasterDmlData", "批量获取采购单失败", throwable);
        }).completable();

        // 找docking extra信息
        List<DockingExtraDTO> dockingListByOrderNo = orderMasterRPCClient.findDockingListByOrderNo(orderNoList);
        if (CollectionUtils.isNotEmpty(dockingListByOrderNo)) {
            orderNoDockingMap = dockingListByOrderNo.stream().collect(Collectors.groupingBy(DockingExtraDTO::getInfo));
        }

        // 运行完后才能添加到插入列表(错误就跳过，先保证主要的东西都有)
        CompletableFuture.allOf(batchFindParentDeptFuture, batchFindApplyFuture).join();

        for (Map<String, Object> dmlData : partition) {
            // init data map
            Map<String, Object> map = ElasticSearchTranslator.parseOrderMasterField(dmlData);
            Integer departmentId = (Integer) map.get("fbuydepartmentid");
            Integer buyAppId = (Integer) map.get("ftbuyappid");

            // 父级部门
            DepartmentDTO parentDept = parentIdDeptMap.get(deptIdToParentIdMap.get(departmentId));
            map.put("department_parent_id", parentDept != null ? parentDept.getId() : StringUtils.EMPTY);
            map.put("department_parent_name", parentDept != null ? parentDept.getName() : StringUtils.EMPTY);
            // 采购单
            ApplicationMasterDTO appMaster = appIdNoMap.get(buyAppId == null ? null : Long.valueOf(buyAppId));
            if (appMaster != null) {
                map.put("relateInfo", appMaster.getRelatedInfo());
                map.put("fbuyapplicationno", appMaster.getApplyNumber());
            }
            // docking extra的信息
            List<DockingExtraDTO> dockingList = orderNoDockingMap.get(map.get("forderno"));
            if (CollectionUtils.isNotEmpty(dockingList)) {
                for (DockingExtraDTO dockingExtra : dockingList) {
                    if (dockingExtra == null){continue;}
                    if (OrderConstant.DOCKING_TYPE == dockingExtra.getType()) {
                        map.put("extra_info", dockingExtra.getExtraInfo());
                    } else if (OrderConstant.DOCKING_STATEMENT_TYPE == dockingExtra.getType()) {
                        map.put("extra_statement", dockingExtra.getExtraInfo());
                    }
                }
            }

            mapList.add(map);
        }
        return mapList;
    }


    /**
     * 增量数据推送到本地部署项目
     * @param dmlData
     */
    private void pushLocalDeployOrderData(DmlData dmlData) {
        List<Map<String, Object>> dataList = dmlData.getData();
        EventType eventType = dmlData.getEventType();
        String tableName = dmlData.getTableName();
        Integer orgId = null;

        // 反查下订单所属得单位，需要本地化部署得单位才推送消息
        OrderMasterDTO orderMaster = new OrderMasterDTO();
        if (ORDER_DETAIL.equals(tableName)) {
            Integer orderId = (Integer) dataList.get(0).get("fmasterid");
            OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
            request.setOrderMasterId(orderId);
            orderMaster = orderMasterRPCClient.findById(request);
        } else if (REF_FUND_CARD_ORDER.equals(tableName)) {
            Object orderIdProperty = dataList.get(0).get("order_id");
            if (orderIdProperty == null || StringUtils.isBlank(orderIdProperty.toString())) {
                return;
            }
            String orderId = (String) orderIdProperty;
            OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
            request.setOrderMasterId(Integer.parseInt(orderId));
            orderMaster = orderMasterRPCClient.findById(request);
        } else if(ORDER_APPROVAL_LOG.equals(tableName)){
            Object orderIdProperty = dataList.get(0).get("order_id");
            if (orderIdProperty == null) {
                return;
            }
            Integer orderId = (Integer) orderIdProperty;
            OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
            request.setOrderMasterId(orderId);
            orderMaster = orderMasterRPCClient.findById(request);
        }

        Map<Integer, String> buyerIdTelephoneMap = null;
        Map<Integer, String> appIdRemarkMap = null;
        List<SyncOrderMessageDTO> syncOrderMessageDTOList = new ArrayList<>(dataList.size());
        for (Map<String, Object> data : dataList) {
            Object species = data.get("species");
            if (OrganizationConstant.LOCAL_DEPLOY_ORG_SET.contains(data.get("fusercode")) || OrganizationConstant.LOCAL_DEPLOY_ORG_SET.contains(orderMaster.getFusercode())) {
                if (OrganizationConstant.GUANG_DONG_YI_KE_DA_XUE.equals(orderMaster.getFusercode()) && species != null && OrderConstant.OFFLINE.equals(species.toString())) {
                    continue;
                }
                SyncOrderMessageDTO messageDTO = null;
                switch (tableName) {
                    case ORDER_MASTER:
                        if (buyerIdTelephoneMap == null) {
                            Set<Integer> buyerIdList = dataList.stream().map(m -> (Integer) m.get("fbuyerid")).collect(Collectors.toSet());
                            Object orgIdData = data.get("fuserid");
                            if (orgIdData == null) {
                                LOGGER.error("本地部署增量同步失败, 订单号{}的fuserid为空", data.get("forderno"));
                                return;
                            }
                            orgId = (Integer) orgIdData;
                            List<UserBaseInfoDTO> userBaseInfoDTOList = userRPCClient.getUserByIdsAndOrgId(buyerIdList, orgId);
                            buyerIdTelephoneMap = userBaseInfoDTOList.stream()
                                    .filter(userBaseInfoDTO -> userBaseInfoDTO.getMobile() != null)
                                    .collect(Collectors.toMap(UserBaseInfoDTO::getId, UserBaseInfoDTO::getMobile, (o, n) -> n));
                        }

                        // 这个卖家留言只保留快照信心，非插入操作不做同步
                        if (appIdRemarkMap == null && EventType.INSERT == eventType) {
                            List<OrderRemarkDTO> orderRemarkList = dataList.stream().map(m -> {
                                Integer buyAppId = (Integer) m.get("ftbuyappid");
                                Integer suppId = (Integer) m.get("fsuppid");
                                OrderRemarkDTO orderRemarkDTO = new OrderRemarkDTO();
                                orderRemarkDTO.setFtbuyappid(buyAppId);
                                orderRemarkDTO.setFsuppid(suppId);

                                return orderRemarkDTO;
                            }).collect(Collectors.toList());

                            List<OrderRemarkDTO> orderRemarkDTOList = orderMasterRPCClient.findByAppIdAndSupplierId(orderRemarkList);
                            appIdRemarkMap = orderRemarkDTOList.stream().collect(Collectors.toMap(OrderRemarkDTO::getFtbuyappid, OrderRemarkDTO::getRemark, (o, n) -> n));
                        }

                        messageDTO = fetchSyncOrderMaster(
                                data,
                                buyerIdTelephoneMap.get(data.get("fbuyerid")),
                                appIdRemarkMap != null ? appIdRemarkMap.get(data.get("ftbuyappid")) : null);
                        break;
                    case ORDER_DETAIL:
                        orgId = orderMaster.getFuserid();
                        messageDTO = fetchSyncOrderDetail(data);
                        messageDTO.setOrgId(orgId);
                        break;
                    case REF_FUND_CARD_ORDER:
                        orgId = orderMaster.getFuserid();
                        messageDTO = fetchSyncOrderFundCard(data);
                        eventType = EventType.UPDATE;
                        messageDTO.setOrgId(orgId);
                        break;
                    case ORDER_APPROVAL_LOG:
                        orgId = orderMaster.getFuserid();
                        if(OrgEnum.HUA_QIAO_DA_XUE.getValue() != orgId){
                            // 不是华侨大学，不推日志
                            return;
                        }
                        messageDTO = fetchSyncOrderLog(data);
                        messageDTO.setOrgId(orgId);
                        break;
                    default:
                        break;
                }

                if (messageDTO != null) {
                    switch (eventType) {
                        case INSERT:
                            messageDTO.setEventType(EventTypeEnum.INSERT.getDescrption());
                            break;
                        case UPDATE:
                            messageDTO.setEventType(EventTypeEnum.UPDATE.getDescrption());
                            break;
                        case DELETE:
                            messageDTO.setEventType(EventTypeEnum.DELETE.getDescrption());
                            break;
                        default:
                            break;
                    }
                }
                syncOrderMessageDTOList.add(messageDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(syncOrderMessageDTOList)) {
            RemoteResponse response = mqRpcService.localPush("77_order_data", orgId, syncOrderMessageDTOList);
            Preconditions.isTrue(response.isSuccess(), "推送订单数据到本地部署失败!" + JsonUtils.toJsonIgnoreNull(response));
        }

    }

    private SyncOrderMessageDTO fetchSyncOrderFundCard(Map<String, Object> data) {
        SyncOrderMessageDTO messageDTO;
        messageDTO = new SyncOrderMessageDTO();
        Preconditions.notNull(data.get("order_id"), "增量更新失败, 绑卡id为空");
        messageDTO.setId(Integer.parseInt(data.get("order_id").toString()));
        messageDTO.setFundCardNo((String) data.get("card_no"));
        return messageDTO;
    }

    private SyncOrderMessageDTO fetchSyncOrderDetail(Map<String, Object> data) {
        SyncOrderMessageDTO messageDTO = new SyncOrderMessageDTO();

        SyncOrderDetailMessageDTO orderDetailMessageDTO = new SyncOrderDetailMessageDTO();
        orderDetailMessageDTO.setId((Integer) data.get("id"));
        orderDetailMessageDTO.setMasterId((Integer) data.get("fmasterid"));
        orderDetailMessageDTO.setProductName((String) data.get("fgoodname"));
        orderDetailMessageDTO.setDangerousType((Integer) data.get("dangerous_type_id"));
        orderDetailMessageDTO.setBrand((String) data.get("fbrand"));
        orderDetailMessageDTO.setProductCode((String) data.get("fgoodcode"));
        orderDetailMessageDTO.setSpec((String) data.get("fspec"));
        orderDetailMessageDTO.setPrice((BigDecimal) data.get("fbidprice"));
        orderDetailMessageDTO.setOriginalPrice((BigDecimal) data.get("original_price"));
        orderDetailMessageDTO.setCoupon((BigDecimal) data.get("remainder_price"));
        orderDetailMessageDTO.setQuantity((BigDecimal) data.get("fquantity"));
        orderDetailMessageDTO.setUnit((String) data.get("funit"));
        orderDetailMessageDTO.setTotalAmount((BigDecimal) data.get("fbidamount"));
        orderDetailMessageDTO.setProductSn(data.get("product_sn").toString());
        orderDetailMessageDTO.setUpdateTime(new Date());

        messageDTO.setOrderDetailList(Arrays.asList(orderDetailMessageDTO));
        return messageDTO;
    }

    private SyncOrderMessageDTO fetchSyncOrderMaster(Map<String, Object> data, String telephone, String buyerMessage) {
        SyncOrderMessageDTO messageDTO;
        messageDTO = new SyncOrderMessageDTO();
        messageDTO.setId((Integer) data.get("id"));
        messageDTO.setOrderNo((String) data.get("forderno"));
        messageDTO.setBuyerDepartmentId((Integer) data.get("fbuydepartmentid"));
        messageDTO.setBuyerDepartmentName((String) data.get("fbuydepartment"));
        messageDTO.setBuyerId((Integer) data.get("fbuyerid"));
        messageDTO.setBuyerName((String) data.get("fbuyername"));
        messageDTO.setOrderDate(data.get("forderdate") != null ? DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, data.get("forderdate").toString()): new Date());
        messageDTO.setStatus((Integer) data.get("status"));
        messageDTO.setTotalAmount((BigDecimal) data.get("forderamounttotal"));
        // 采购人手机
        messageDTO.setTelephone(telephone);
        messageDTO.setReceiptUser((String) data.get("fbuyercontactman"));
        messageDTO.setReceiptAddress((String) data.get("fbiderdeliveryplace"));
        messageDTO.setReceiptUserTelephone((String) data.get("fbuyertelephone"));
        messageDTO.setBuyerMessage(buyerMessage);
        // 暂时不做显示
//        messageDTO.setReceiptUserFixedPhone("");
//        messageDTO.setDeliveryMode();
        Timestamp timestamp = (Timestamp) data.get("fdeliverydate");
        if (timestamp != null) {
            messageDTO.setDeliveryDate(new Date(timestamp.getTime()));
        }
        messageDTO.setSupplierId((Integer) data.get("fsuppid"));
        messageDTO.setSupplierName((String) data.get("fsuppname"));
        messageDTO.setTransportFee((BigDecimal) data.get("carry_fee"));
        messageDTO.setReceiveImageUrls((String) data.get("receive_pic_urls"));
        messageDTO.setFundStatus((Integer) data.get("fund_status"));
        messageDTO.setFailedReason((String) data.get("failed_reason"));
        // invoice_title_id 是Long类型
        messageDTO.setInvoiceTitleId(data.get("invoice_title_id") != null ? Integer.parseInt(data.get("invoice_title_id").toString()) : 0);
        messageDTO.setDeliveryNo((String) data.get("delivery_no"));
        messageDTO.setCancelReason((String) data.get("fcancelreason"));
        messageDTO.setUpdateTime(new Date());
        messageDTO.setOrgId((Integer) data.get("fuserid"));
        return messageDTO;
    }

    private SyncOrderMessageDTO fetchSyncOrderLog(Map<String, Object> data){
        SyncOrderLogMessageDTO log = new SyncOrderLogMessageDTO()
                .setId((Integer) data.get("id"))
                .setOrderId((Integer) data.get("order_id"))
                .setPhoto((String) data.get("photo"))
                .setReason((String) data.get("reason"))
                .setApproveLevel((Integer) data.get("approve_level"))
                .setOperatorId((Integer) data.get("operator_id"))
                .setOpUserType((Integer) data.get("op_user_type"));
        Timestamp timestamp = (Timestamp) data.get("creation_time");
        if(timestamp != null){
            log.setCreationTime(new Date(timestamp.getTime()));
        }
        Byte approveStatusByte = (Byte) data.get("approve_status");
        if (approveStatusByte != null) {
            log.setApproveStatus(approveStatusByte.intValue());
        }

        SyncOrderMessageDTO msgDTO = new SyncOrderMessageDTO();
        msgDTO.setOrderLogList(Collections.singletonList(log));
        return msgDTO;
    }

}
