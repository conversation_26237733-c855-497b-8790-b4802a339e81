package com.ruijing.sync.order.service.compensate;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.google.common.collect.Lists;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.message.api.dto.MessageDTO;
import com.ruijing.message.api.enums.MessageTypeEnum;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.sync.common.dto.SyncDataRequestDTO;
import com.ruijing.sync.order.rpc.EmailRPCClient;
import com.ruijing.sync.order.rpc.OrderMasterRPCClient;
import com.ruijing.sync.order.rpc.OrderSearchRPCClient;
import com.ruijing.sync.order.utils.CacheUtils;
import com.ruijing.sync.order.utils.ExcelUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2021/10/12 10:04
 */
@Service
public class CheckAbnormalService {

    private final static String CAT_TYPE = "CheckAbnormalService";

    @Resource
    private OrderSearchRPCClient orderSearchClient;

    @Resource
    private OrderMasterRPCClient orderMasterRPCClient;

    @Resource
    private EmailRPCClient emailRPCClient;

    @Resource
    private CompensateDataService compensateDataService;

    @PearlValue(key = "check.abnormal.email.receiver",defaultValue = "<EMAIL>")
    private String emailReceiver;

    /**
     * 将mysql和es的差异信息写入excel
     * @param request
     * @return
     */
    public Boolean writeAbnormalDataToExcel(SyncDataRequestDTO request) {
        String methodName = "writeAbnormalDataToExcel";
        CacheUtils.controlRepeatOperation(methodName);
        List<Integer> orderIdList = this.checkAbnormalData(request);
        return compensateDataService.sendDingTalk(orderIdList);
    }

    /**
     * 获取返回指定时间或数据的异常订单号（最后将之记录到文件中）
     * @param request
     * @return
     */
    public List<Integer> checkAbnormalData(SyncDataRequestDTO request) {
        Integer lastFewDays = request.getLastFewDays();
        Integer lastHours = request.getLastHours();
        Integer lastMinutes = request.getLastMinutes();
        List<String> numberList = request.getNumberList();
        if (lastFewDays != null && lastFewDays > 0) {
            ZonedDateTime zonedDateTime = LocalDateTime.now().plusDays(-(lastFewDays)).atZone(ZoneId.systemDefault());
            return this.getAbnormalListByTime(zonedDateTime);
        } else if (lastFewDays != null && lastFewDays == -1L) {
            return this.getAbnormalListAll();
        } else if (lastHours != null && lastHours > 0) {
            ZonedDateTime zonedDateTime = LocalDateTime.now().plusHours(-(lastHours)).atZone(ZoneId.systemDefault());
            return this.getAbnormalListByTime(zonedDateTime);
        } else if (lastMinutes != null && lastMinutes > 0) {
            ZonedDateTime zonedDateTime = LocalDateTime.now().plusMinutes(-(lastMinutes)).atZone(ZoneId.systemDefault());
            return this.getAbnormalListByTime(zonedDateTime);
        } else if (CollectionUtils.isNotEmpty(numberList)) {
            return this.getAbnormalListByOrderNo(numberList);
        }
        return null;
    }

    /**
     * 按照订单号，查询订单是否mysql和es不同
     * @param orderNoList
     * @return
     */
    private List<Integer> getAbnormalListByOrderNo(List<String> orderNoList) {
        List<OrderMasterDTO> masterDBList = orderMasterRPCClient.findByOrderOrderNoList(orderNoList);
        List<OrderMasterSearchDTO> masterESList = orderSearchClient.getOrderByOrderNoList(orderNoList);
        Map<Integer, OrderMasterDTO> masterDBMap = masterDBList.stream().collect(Collectors.toMap(OrderMasterDTO::getId, Function.identity(), (oldVal, newVal) -> newVal));
        Map<Integer, OrderMasterSearchDTO> masterESMap = masterESList.stream().collect(Collectors.toMap(OrderMasterSearchDTO::getId, Function.identity(), (oldVal, newVal) -> newVal));
        return this.checkDataBetweenDBAndES(masterESMap, masterDBMap);
    }

    /**
     * 全部订单，通过id范围查询；
     * @return
     */
    private List<Integer> getAbnormalListAll() {
        List<Integer> abnormalList = New.list();
        Integer maxId = orderMasterRPCClient.findMaxId();
        int count = 1;
        while (count < maxId) {
            Integer lower = count;
            Integer upper = count + 500;
            // 获取mysql和es的数据
            Map<Integer, OrderMasterDTO> masterDBMap = new HashMap<>();
            Map<Integer, OrderMasterSearchDTO> masterESMap = new HashMap<>();
            try {
                Pair<Map<Integer, OrderMasterDTO>, Map<Integer, OrderMasterSearchDTO>> mysqlEsDataMap = compensateDataService.getMysqlEsDataMap(lower, upper);
                masterDBMap = mysqlEsDataMap.getLeft();
                masterESMap = mysqlEsDataMap.getRight();
            } catch (Exception e) {
                Cat.logError(CAT_TYPE, "checkAbnormalListAll", "按照id范围查询数据库或es出错了", e);
                return New.list(-1);
            }
            abnormalList.addAll(this.checkDataBetweenDBAndES(masterESMap, masterDBMap));
            count = upper;
        }
        return abnormalList;
    }

    /**
     * 按照时间查询和检查, 返回-1表示出错
     * @param zonedDateTime
     * @return
     */
    private List<Integer> getAbnormalListByTime(ZonedDateTime zonedDateTime) {
        List<Integer> lastUpdatedId = orderMasterRPCClient.findIdByUpdateTimeAfter(Date.from(zonedDateTime.toInstant()));
        List<List<Integer>> partition = Lists.partition(lastUpdatedId, 500);
        List<Integer> abnormalList = New.list();
        for (List<Integer> partIdList : partition) {
            // 获取mysql和es的数据
            Map<Integer, OrderMasterDTO> masterDBMap = new HashMap<>();
            Map<Integer, OrderMasterSearchDTO> masterESMap = new HashMap<>();
            try {
                Pair<Map<Integer, OrderMasterDTO>, Map<Integer, OrderMasterSearchDTO>> mysqlEsDataMap = compensateDataService.getMysqlEsDataMap(partIdList);
                masterDBMap = mysqlEsDataMap.getLeft();
                masterESMap = mysqlEsDataMap.getRight();
            } catch (Throwable e) {
                Cat.logError(CAT_TYPE, "getAbnormalListByTime", "按照id列表查询数据库或es出错了", e);
                return New.list(-1);
            }
            // 检查：
            abnormalList.addAll(this.checkDataBetweenDBAndES(masterESMap, masterDBMap));
        }
        return abnormalList;
    }

    /**
     * 通过来自es和mysql的两个map判断哪些单具有异常情况；
     * @param dataFromES
     * @param dataFromDB
     * @return
     */
    private List<Integer> checkDataBetweenDBAndES(Map<Integer, OrderMasterSearchDTO> dataFromES, Map<Integer, OrderMasterDTO> dataFromDB) {
        List<Integer> abnormalList = New.list();
        if (dataFromDB == null) {
            return New.list(-1);
        }
        for (Map.Entry<Integer, OrderMasterDTO> entry : dataFromDB.entrySet()) {
            OrderMasterSearchDTO masterFromES = dataFromES.get(entry.getKey());
            if (masterFromES == null) {
                abnormalList.add(entry.getKey());
                continue;
            }
            // 异常校验
            boolean abnormal = (!Objects.equals(entry.getValue().getStatus(), masterFromES.getStatus())
                    || !Objects.equals(entry.getValue().getInventoryStatus().intValue(), masterFromES.getInventoryStatus())
                    || !Objects.equals(entry.getValue().getFundStatus(), masterFromES.getFundStatus()));
            if (abnormal) {
                abnormalList.add(entry.getKey());
            }
        }
        return abnormalList;
    }
}
