package com.ruijing.sync.order.client;

import com.google.common.collect.Lists;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.search.curd.reponse.BulkResponse;
import com.ruijing.search.curd.request.BulkDeleteRequest;
import com.ruijing.search.curd.request.BulkRequest;
import com.ruijing.search.curd.request.DeleteItemRequest;
import com.ruijing.search.curd.request.UpdateItemRequest;
import com.ruijing.search.curd.service.SearchUpdateService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * @description: 搜索API RPC 客户端
 * @author: zhongyulei
 * @create: 2020/11/18 16:57
 **/
@ServiceClient
public class SearchAPIClient {

    @MSharpReference(remoteAppkey = "msharp-search-service", group = "pub")
    private SearchUpdateService searchUpdateService;

    private final Logger logger = LoggerFactory.getLogger(SearchAPIClient.class);

    public void bulkUpdate(BulkRequest request) {
        if (CollectionUtils.isEmpty(request.getRequests()) && CollectionUtils.isEmpty(request.getDeleteItemRequests())) {
            return;
        }
        if (request.getRequests().size() <= 100) {
            bulkUpdateSingle(request);
        } else {
            bulkUpdateBatch(request);
        }
    }

    // 批量的更新
    public void bulkUpdateBatch(BulkRequest request) {
        Transaction transaction = Cat.newTransaction("SearchAPIClient", "bulkUpdateBatch");
        try {
            List<UpdateItemRequest> requests = request.getRequests();
            //每次最多提交100个批量请求
            List<List<UpdateItemRequest>> partitions = Lists.partition(requests, 100);
            for (List<UpdateItemRequest> partition : partitions) {
                BulkRequest bulkRequest = new BulkRequest();
                bulkRequest.setKey(request.getKey());
                bulkRequest.addUpdateRequests(partition);
                bulkUpdateSingle(bulkRequest);
             }
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            transaction.addData(CatUtils.buildStackInfo("批量更新索引数据失败！", e));
            logger.error("批量更新索引数据失败！{}", e);
            throw new IllegalStateException("批量更新索引数据失败！" + e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    // 单次的更新
    public void bulkUpdateSingle(BulkRequest request) {
        Transaction transaction = Cat.newTransaction("SearchAPIClient", "bulkUpdateSingle");
        try {
            BulkResponse response = searchUpdateService.bulkUpdate(request);
            Preconditions.isTrue(response.isSuccess(), "批量更新索引数据失败！" + JsonUtils.toJsonIgnoreNull(response));
            Preconditions.isTrue(response.getFailed() <= 0, "批量更新索引数据失败！" + JsonUtils.toJsonIgnoreNull(response));
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            transaction.addData(CatUtils.buildStackInfo("批量更新索引数据失败！", e));
            logger.error("批量更新索引数据失败！{}", e);
            throw new IllegalStateException("批量更新索引数据失败！" + e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    /**
     * 按照主键 批量删除es数据
     * @param bulkDeleteRequest
     */
    public void bulkDelete(BulkDeleteRequest bulkDeleteRequest) {
        List<DeleteItemRequest> requests = bulkDeleteRequest.getRequests();
        List<List<DeleteItemRequest>> partition = Lists.partition(requests, 100);
        for (List<DeleteItemRequest> part : partition) {
            BulkDeleteRequest curBulkDelReq = new BulkDeleteRequest();
            curBulkDelReq.setKey(bulkDeleteRequest.getKey());
            curBulkDelReq.addRequests(New.list(part));
            searchUpdateService.bulkDelete(bulkDeleteRequest);
        }
    }
}
