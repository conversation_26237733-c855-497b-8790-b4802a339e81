package com.ruijing.sync.order.rpc;

import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.reagent.order.base.order.service.OrderAddressRPCService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.sync.order.log.annotation.ServiceLog;
import com.ruijing.sync.order.log.enums.ServiceType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: <PERSON>g <PERSON>
 * @Description:
 * @DateTime: 2022/7/7 15:31
 */
@ServiceClient
@CatAnnotation
public class OrderGalaxyRpcClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderAddressRPCService orderAddressRPCService;

    @ServiceLog(description = "查询配送地址接口", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderAddressDTO> findByOrderId(List<Integer> orderIdList) {
        Preconditions.notEmpty(orderIdList, "orderIdList must not be empty");
        List<Long> orderIdParams = orderIdList.stream().map(Integer::longValue).collect(Collectors.toList());
        OrderBaseParamDTO request = new OrderBaseParamDTO();
        request.setOrderIdList(orderIdParams);
        RemoteResponse<List<OrderAddressDTO>> response = orderAddressRPCService.listByOrderId(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }
}
