package com.ruijing.sync.order.service.compensate;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.msharp.single.jdbc.util.CatUtil;
import com.ruijing.fundamental.alarm.client.dingtalk.DingTalkClient;
import com.ruijing.fundamental.alarm.dingtalk.client.request.At;
import com.ruijing.fundamental.alarm.dingtalk.client.request.Text;
import com.ruijing.fundamental.alarm.dingtalk.client.request.TextSendRequest;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.job.core.annotation.JobExecutor;
import com.ruijing.job.core.annotation.JobHandler;
import com.ruijing.job.core.annotation.JobRegister;
import com.ruijing.job.core.context.JobContext;
import com.ruijing.job.core.model.TriggerInfo;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.search.curd.request.BulkDeleteRequest;
import com.ruijing.search.curd.request.BulkRequest;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.sync.common.api.CompensateDataRPCService;
import com.ruijing.sync.common.dto.SyncDataRequestDTO;
import com.ruijing.sync.order.client.SearchAPIClient;
import com.ruijing.sync.order.constant.OrderConstant;
import com.ruijing.sync.order.rpc.*;
import com.ruijing.sync.order.service.SyncFullService;
import com.ruijing.sync.order.utils.CacheUtils;
import com.ruijing.sync.order.utils.ExcelUtils;
import jdk.nashorn.internal.scripts.JO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2022/8/16 14:55
 */
@Service
@JobExecutor
public class CompensateDataService implements CompensateDataRPCService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private CacheClient cacheClient;

    @Resource
    private OrderMasterRPCClient orderMasterRPCClient;

    @Resource
    private SyncFullService syncFullService;

    @Resource
    private OrderSearchRPCClient orderSearchClient;

    @Resource
    private SearchAPIClient searchAPIClient;

    @Resource
    private EmailRPCClient emailRPCClient;

    @Resource
    private DingTalkAlarmClient dingTalkAlarmClient;

    @PearlValue(key="check.abnormal.start.id", defaultValue = "198000")
    private Integer startId;

    @PearlValue(key = "check.abnormal.email.receiver",defaultValue = "<EMAIL>")
    private String emailReceiver;

    private final static String CAT_TYPE = "CheckAbnormalService";

    /**
     * 同步mysql到es，仅master表
     */
    @Override
    public RemoteResponse<Boolean> compensateAllData(SyncDataRequestDTO request) {
        // 用订单id更新时避免覆盖处理（仅补偿时使用）
        synchronized (this) {
            if (OrderConstant.compensationFlag == true) {
                Preconditions.isTrue(false, "正在处理其他补偿请求，请稍等");
            } else {
                OrderConstant.compensationFlag = true;
            }
            OrderConstant.compensationIncreRedisFlag = true;
        }
        // 分批处理
        List<Integer> allAbnormalList = New.list();
        try {
            // 区分补偿方案
            if (request.getLastFewDays() != null && request.getLastFewDays() == -1) {
                allAbnormalList = this.checkAbnormalListAll();
            } else if (request.getLastFewDays() != null && request.getLastFewDays() > 0) {
                Preconditions.isTrue(request.getLastFewDays() <= 360, "需要同步的天数较多，请直接全量同步");
                ZonedDateTime zonedDateTime = LocalDate.now().plusDays(-(request.getLastFewDays())).atStartOfDay().atZone(ZoneId.systemDefault());
                allAbnormalList = this.checkAbnormalListLastNTime(Date.from(zonedDateTime.toInstant()), false);
            } else if (request.getLastHours() != null && request.getLastHours() > 0) {
                Preconditions.isTrue(request.getLastHours() <= 24, "同步大于24小时，请选择按天或者全量同步");
                ZonedDateTime zonedDateTime = LocalDateTime.now().plusHours(-(request.getLastHours())).atZone(ZoneId.systemDefault());
                allAbnormalList = this.checkAbnormalListLastNTime(Date.from(zonedDateTime.toInstant()), true);
            } else if (request.getLastMinutes() != null && request.getLastMinutes() > 0) {
                Preconditions.isTrue(request.getLastMinutes() <= 60, "同步大于60min，请选择按小时或者全量同步");
                ZonedDateTime zonedDateTime = LocalDateTime.now().plusMinutes(-(request.getLastMinutes())).atZone(ZoneId.systemDefault());
                allAbnormalList = this.checkAbnormalListLastNTime(Date.from(zonedDateTime.toInstant()), true);
            }
            else {
                throw new IllegalStateException("暂不支持此入参的数据补偿方案，lastFewDays请传入-1(全量)或正整数");
            }
        } catch (Throwable e) {
            logger.error("批量补偿数据出错：",e);
            Cat.logError(CAT_TYPE, "compensateAllData", "批量补偿数据出错", e);
            Preconditions.isTrue(false, "批量补偿数据出错, msg=" + e.getMessage());
        } finally {
            OrderConstant.compensationIncreRedisFlag = false;
            OrderConstant.compensationFlag = false;
            cacheClient.removeCache(OrderConstant.ADDI_COMPENSATE_KEY);
        }
        // 构建excel文件并发送
        if (CollectionUtils.isNotEmpty(allAbnormalList)) {
            this.sendDingTalk(allAbnormalList);
        }
        return RemoteResponse.success();
    }

    @JobHandler
    @JobRegister(phone = "***********", scheduleCron = "0 0/10 8-19 * * ?")
    public void compensateAllDataByTime() {
        // 获取入参
        TriggerInfo triggerInfo = JobContext.getTriggerInfo();
        String executorParam = triggerInfo.getExecutorParam();
        SyncDataRequestDTO req = JsonUtils.fromJson(executorParam, SyncDataRequestDTO.class);

        // 执行服务
        this.compensateAllData(req);
    }

    /**
     * 全部订单补偿，通过id范围查询；
     * @return
     */
    private List<Integer> checkAbnormalListAll() {
        Integer maxId = orderMasterRPCClient.findMaxId();
        int count = startId;
        List<Integer> allAbnormalList = New.list();
        while (count < maxId) {
            Integer lower = count;
            Integer upper = count + 500;
            // 获取mysql和es的数据
            Map<Integer, OrderMasterDTO> masterDBMap = new HashMap<>();
            Map<Integer, OrderMasterSearchDTO> masterESMap = new HashMap<>();
            try {
                Pair<Map<Integer, OrderMasterDTO>, Map<Integer, OrderMasterSearchDTO>> mysqlEsDataMap = this.getMysqlEsDataMap(lower, upper);
                masterDBMap = mysqlEsDataMap.getLeft();
                masterESMap = mysqlEsDataMap.getRight();
            } catch (Throwable e) {
                Cat.logError(CAT_TYPE, "checkAbnormalListAll", "按照id范围查询数据库或es出错了", e);
                return New.list(-1);
            }
            // 分情况，补偿数据.(1)存在mysql，更新es(2)存在es不存在mysql，删掉es数据
            this.compensateAndGetAbnormalList(masterESMap, masterDBMap, allAbnormalList);
            count = upper;
        }
        return allAbnormalList;
    }

    /**
     * 前n天的数据补偿，通过id范围查询；
     * @param datePointAfter
     * @return
     */
    private List<Integer> checkAbnormalListLastNTime(Date datePointAfter, boolean isHoursBefore) {
//        ZonedDateTime zonedDateTime = LocalDate.now().plusDays(-(lastFewDays)).atStartOfDay().atZone(ZoneId.systemDefault());
        List<Integer> lastUpdatedId = orderMasterRPCClient.findIdByUpdateTimeAfter(datePointAfter);
        if (isHoursBefore) {
            // hours的增量补偿，需要控制条目，防止全量改表导致补偿阻塞
            lastUpdatedId = lastUpdatedId.subList(0, Math.min(lastUpdatedId.size(), 10000));
        }
        List<List<Integer>> lastUpdatedIdPartition = Lists.partition(lastUpdatedId, 500);
        List<Integer> allAbnormalList = New.list();
        for (List<Integer> partitionList : lastUpdatedIdPartition) {
            // 获取mysql和es的数据
            Map<Integer, OrderMasterDTO> masterDBMap = new HashMap<>();
            Map<Integer, OrderMasterSearchDTO> masterESMap = new HashMap<>();
            try {
                Pair<Map<Integer, OrderMasterDTO>, Map<Integer, OrderMasterSearchDTO>> mysqlEsDataMap = this.getMysqlEsDataMap(New.list(partitionList));
                masterDBMap = mysqlEsDataMap.getLeft();
                masterESMap = mysqlEsDataMap.getRight();
            } catch (Exception e) {
                Cat.logError(CAT_TYPE, "checkAbnormalListLastNTime", "按照id列表查询数据库或es出错了", e);
                return New.list(-1);
            }
            // 分情况，补偿数据.(1)存在mysql，更新es(2)存在es不存在mysql，删掉es数据
            this.compensateAndGetAbnormalList(masterESMap, masterDBMap, allAbnormalList);
        }
        return allAbnormalList;
    }

    /**
     * 分情况，补偿数据.(1)存在mysql，更新es(2)存在es不存在mysql，删掉es数据，并且将异常数据放入到abnormal列表中
     * @param masterESMap
     * @param masterDBMap
     * @param allAbnormalList
     */
    private void compensateAndGetAbnormalList(Map<Integer, OrderMasterSearchDTO> masterESMap,
                                              Map<Integer, OrderMasterDTO> masterDBMap,
                                              List<Integer> allAbnormalList) {
        Pair<Set<Integer>, Set<Integer>> difDataBetweenDBAndES = this.getDifDataBetweenDBAndES(masterESMap, masterDBMap);
        Set<Integer> existInEs = difDataBetweenDBAndES.getLeft();
        Set<Integer> existInMysql = difDataBetweenDBAndES.getRight();
        if (CollectionUtils.isNotEmpty(existInMysql)) {
            this.compensateMysqlToEs(existInMysql);
        }
        if (CollectionUtils.isNotEmpty(existInEs)) {
            this.deleteRedundantEs(existInEs);
        }

        // 存到全部异常列表，方便后续发邮件和记日志
        for (Integer abnormalEs : existInEs) {
            allAbnormalList.add(abnormalEs);
        }
        for (Integer abnormalMysql : existInMysql) {
            allAbnormalList.add(abnormalMysql);
        }
    }

    /**
     * 根据id范围 获取mysql（左）和es(右)的数据 map
     * @param lower
     * @param upper
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public Pair<Map<Integer, OrderMasterDTO>, Map<Integer, OrderMasterSearchDTO>> getMysqlEsDataMap(Integer lower, Integer upper) throws ExecutionException, InterruptedException {
        CompletableFuture<List<OrderMasterDTO>> masterDBFuture = AsyncExecutor
                .listenableCallAsync(() -> orderMasterRPCClient.rangeById(lower, upper))
                .addFailureCallback(throwable -> {
                    Cat.logError(CAT_TYPE, "getAbnormalListAll", "异步获取mysql范围订单id的方法失败", throwable);})
                .completable();

        CompletableFuture<List<OrderMasterSearchDTO>> masterESFuture = AsyncExecutor.listenableCallAsync(() -> orderSearchClient.getMasterSearchByIdRange(lower, upper))
                .addFailureCallback(throwable -> {
                    Cat.logError(CAT_TYPE, "getAbnormalListAll", "异步获取es范围订单id的方法失败", throwable);
                })
                .completable();

        CompletableFuture.allOf(masterDBFuture, masterESFuture).exceptionally(throwable -> {
            throw new IllegalStateException("按照id范围查询数据库或es出错," + throwable.getMessage());
        }).join();

        Map<Integer, OrderMasterDTO> masterDBMap = new HashMap<>();
        Map<Integer, OrderMasterSearchDTO> masterESMap = new HashMap<>();
        try {
            List<OrderMasterDTO> masterDBList = masterDBFuture.get();
            List<OrderMasterSearchDTO> masterESList = masterESFuture.get();
            masterDBMap = masterDBList.stream().collect(Collectors.toMap(OrderMasterDTO::getId, Function.identity(), (oldVal, newVal) -> newVal));
            masterESMap = masterESList.stream().collect(Collectors.toMap(OrderMasterSearchDTO::getId, Function.identity(), (oldVal, newVal) -> newVal));
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "getMysqlEsDataMap", "按照id范围查询数据库或es出错了", e);
            throw e;
        }
        return Pair.of(masterDBMap, masterESMap);
    }

    /**
     * 根据订单id列表 获取mysql（左）和es(右)的数据 map
     * @param partIdList
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public Pair<Map<Integer, OrderMasterDTO>, Map<Integer, OrderMasterSearchDTO>> getMysqlEsDataMap(List<Integer> partIdList) throws ExecutionException, InterruptedException {
        // 同时查询mysql和es
        CompletableFuture<List<OrderMasterDTO>> masterDBFuture = AsyncExecutor.listenableCallAsync(() -> orderMasterRPCClient.findByOrderIdSet(new HashSet<>(partIdList)))
                .addFailureCallback(throwable -> {
                    Cat.logError(CAT_TYPE, "getAbnormalListByTime", "按照时间查询mysql数据库出错", throwable);
                }).completable();

        CompletableFuture<List<OrderMasterSearchDTO>> masterESFuture = AsyncExecutor.listenableCallAsync(() -> orderSearchClient.getOrderByIdList(partIdList))
                .addFailureCallback(throwable -> {
                    Cat.logError(CAT_TYPE, "getAbnormalListByTime", "按照时间查询es数据库出错", throwable);
                }).completable();

        CompletableFuture.allOf(masterDBFuture, masterESFuture).exceptionally(throwable -> {
            throw new IllegalStateException("按照时间查询es数据库出错," + throwable.getMessage());
        }).join();

        Map<Integer, OrderMasterDTO> masterDBMap = new HashMap<>();
        Map<Integer, OrderMasterSearchDTO> masterESMap = new HashMap<>();
        try {
            List<OrderMasterDTO> masterDBList = masterDBFuture.get();
            List<OrderMasterSearchDTO> masterESList = masterESFuture.get();
            masterDBMap = masterDBList.stream().collect(Collectors.toMap(OrderMasterDTO::getId, Function.identity(), (oldVal, newVal) -> newVal));
            masterESMap = masterESList.stream().collect(Collectors.toMap(OrderMasterSearchDTO::getId, Function.identity(), (oldVal, newVal) -> newVal));
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "getAbnormalListByTime", "按照时间查询数据库或es出错了", e);
            throw e;
        }
        return Pair.of(masterDBMap, masterESMap);
    }

    /**
     * 获取es 和 mysql 不同的数据
     * @param dataFromES
     * @param dataFromDB
     * @return pair.left=存在es但不存在mysql的订单id；pair.right=存在mysql但不存在es的数据，或者mysql的状态（订单状态，入库状态，经费状态等条件）不一致的订单id
     */
    private Pair<Set<Integer>, Set<Integer>> getDifDataBetweenDBAndES(Map<Integer, OrderMasterSearchDTO> dataFromES, Map<Integer, OrderMasterDTO> dataFromDB) {
        Set<Integer> existInMysql = new HashSet<>();
        Set<Integer> existInEs = new HashSet<>();
        if (dataFromDB == null) {
            return Pair.of(existInEs, existInMysql);
        }
        // 在mysql不在es，或mysql的状态与es不匹配（以mysql为准）
        for (Map.Entry<Integer, OrderMasterDTO> entry : dataFromDB.entrySet()) {
            OrderMasterSearchDTO masterFromES = dataFromES.get(entry.getKey());
            if (masterFromES == null) {
                existInMysql.add(entry.getKey());
                continue;
            }
            // 异常校验
            boolean statusAbnormal = !Objects.equals(entry.getValue().getStatus(), masterFromES.getStatus());
            boolean fundAbnormal = !Objects.equals(entry.getValue().getFundStatus(), masterFromES.getFundStatus());
            boolean inventoryAbnormal = !Objects.equals(entry.getValue().getInventoryStatus().intValue(), masterFromES.getInventoryStatus());

            if (statusAbnormal || fundAbnormal || inventoryAbnormal) {
                existInMysql.add(entry.getKey());
            }
        }
        // 在es 不在mysql
        for (Map.Entry<Integer, OrderMasterSearchDTO> entry : dataFromES.entrySet()) {
            OrderMasterDTO masterFromDB = dataFromDB.get(entry.getKey());
            if (masterFromDB == null) {
                existInEs.add(entry.getKey());
            }
        }
        return Pair.of(existInEs, existInMysql);
    }

    /**
     * 更新mysql数据到es中，同时控制覆盖问题
     * @param masterIdSet
     */
    private void compensateMysqlToEs(Set<Integer> masterIdSet) {
        boolean getLock = false;
        Transaction catTransaction = Cat.newTransaction(this.getClass().getSimpleName(), "compensateMysqlToEs", true);
        try {
            getLock = OrderConstant.reentrantLock.tryLock(10, TimeUnit.SECONDS);
            if (!getLock) {
                throw new IllegalStateException("增量同步出现卡顿，或者锁使用失败，请排查死锁和检查代码！");
            }
            // 仅同步，在补偿过程中没有发生增量同步的数据
            cacheClient.setOrAdd(OrderConstant.FULL_COMPENSATE_KEY, masterIdSet);
            Set<Integer> difOrderIdSet = cacheClient.sdiff(OrderConstant.FULL_COMPENSATE_KEY, OrderConstant.ADDI_COMPENSATE_KEY);
            List<OrderMasterDTO> orderMasterList = orderMasterRPCClient.findByOrderIdSet(masterIdSet);

            List<OrderMasterDTO> difMasterList = New.list();
            for (OrderMasterDTO master : orderMasterList) {
                if (difOrderIdSet.contains(master.getId())) {
                    difMasterList.add(master);
                }
            }
            BulkRequest request = new BulkRequest();
            request.setKey(OrderConstant.FULL_KEY);
            syncFullService.BulkUpdateOrderToSearch(request, difMasterList);
            // 钉钉告警
            IllegalStateException eStatus = new IllegalStateException("存在增量同步失败数据，执行补偿同步");
            catTransaction.setStatus(eStatus);
            catTransaction.addData(JsonUtils.toJson(masterIdSet), eStatus);
        } catch (Throwable e) {
            logger.error("批量补偿数据出错：",e);
            // 补偿同步失败钉钉告警
            catTransaction.setStatus(e);
            catTransaction.addData(CatUtils.buildStackInfo("补偿同步出错", e));
            catTransaction.addData(JsonUtils.toJson(masterIdSet), e);
        } finally {
            catTransaction.complete();
            // 解锁，删除当前批次的订单id缓存
            if (getLock) {
                OrderConstant.reentrantLock.unlock();
            }
            cacheClient.removeCache(OrderConstant.FULL_COMPENSATE_KEY);
        }
    }

    /**
     * 批量删除es数据
     * @param masterIdSet
     */
    private void deleteRedundantEs(Set<Integer> masterIdSet) {
        if (CollectionUtils.isEmpty(masterIdSet)) {
            return;
        }
        BulkDeleteRequest bulkDelReq = new BulkDeleteRequest();
        bulkDelReq.setKey(OrderConstant.FULL_KEY);
        List<String> masterIdStringList = New.list(masterIdSet).stream().map(s -> s.toString()).collect(Collectors.toList());
        bulkDelReq.addIds(masterIdStringList);
        searchAPIClient.bulkDelete(bulkDelReq);
    }

    /**
     * 导出异常excel并发送邮件
     * @param abnormalOrderIdList
     * @return
     */
    public boolean exportAndSendExcel(List<Integer> abnormalOrderIdList) {
        String methodName = "writeAbnormalDataToExcel";
        CacheUtils.controlRepeatOperation(methodName);

        List<List<Integer>> partition = Lists.partition(abnormalOrderIdList, 500);
        ExcelWriter writer = null;
        String format = DateUtils.format("yyyyMMddHHmmss", new Date());
        String fileName = "Mysql与ES不同订单" + format;
        File file = new File(fileName + ".xlsx");
        try {
            writer = EasyExcelFactory.write(file).build();
            WriteSheet sheet;
            List<List<String>> header = New.list(New.list("订单id"), New.list("订单号"));
            sheet = EasyExcelFactory.writerSheet("Mysql与ES不同订单").head(header).registerWriteHandler(ExcelUtils.getCellStyleStrategy()).build();
            for (List<Integer> part : partition) {
                List<OrderMasterDTO> masterDBList = orderMasterRPCClient.findByOrderIdSet(new HashSet<>(part));
                writer.write(ExcelUtils.constructAbnormalOrderRecord(masterDBList), sheet);
            }
            writer.finish();
            // 发邮件
            String[] emailArray = {emailReceiver};
            emailRPCClient.sendFileToEmail(emailArray, file, fileName);
        } catch (Throwable e) {
            Cat.logError(CAT_TYPE, "exportAndSendExcel", "差异信息导出异常", e);
            return false;
        } finally {
            CacheUtils.removeCache(methodName);
            if (writer != null) {
                writer.finish();
            }
            if (file != null) {
                file.delete();
            }
        }
        return true;
    }

    /**
     * 发送dingtalk到订单组
     * @param abnormalOrderIdList
     * @return
     */
    public boolean sendDingTalk(List<Integer> abnormalOrderIdList) {
        List<List<Integer>> partition = Lists.partition(abnormalOrderIdList, 500);
        try {
            for (List<Integer> part : partition) {
                String join = StringUtils.join(part, ", ");
                dingTalkAlarmClient.sendDingMessage(OrderConstant.ORDER_GROUP_WEB_HOOK, OrderConstant.SYNC_ALARM_AT_PHONE_LIST, join, false);
            }
        } catch (Throwable e) {
            Cat.logError(CAT_TYPE, "sendDingTalk", "差异信息钉钉告警异常", e);
            return false;
        }
        return true;
    }
}
