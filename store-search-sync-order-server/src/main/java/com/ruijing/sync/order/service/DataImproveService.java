package com.ruijing.sync.order.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.search.curd.request.BulkRequest;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.sync.common.dto.SyncDataRequestDTO;
import com.ruijing.sync.order.client.SearchAPIClient;
import com.ruijing.sync.order.constant.OrderConstant;
import com.ruijing.sync.order.rpc.OrderMasterRPCClient;
import com.ruijing.sync.order.rpc.UserRPCClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: Zeng Yanru
 * @Description: 用于数据完善，旧数据处理等的脚本
 * @DateTime: 2022/7/6 10:03
 */
@Service
public class DataImproveService {

    @Resource
    private UserRPCClient userRPCClient;

    @Resource
    private OrderMasterRPCClient orderMasterRPCClient;

    @Resource
    private SearchAPIClient searchAPIClient;

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "DataImproveService";

    /**
     * 部门id，对应父一级部门的id和名称(因为是后门全量，调用量很小，因此过期策略为expire after access)
     */
    private Cache<Integer, Pair<Integer, String>> deptParentInfoCache
            = CacheBuilder.newBuilder().maximumSize(1000).expireAfterAccess(1800, TimeUnit.SECONDS).build();

    /**
     * 全量同步订单采购部门父部门信息(仅用于初始化补充，不用于后续补全或更新数据)
     * @param request
     * @return
     */
    public Boolean supplementDeptParentData(SyncDataRequestDTO request) {
        Preconditions.isTrue("<EMAIL>".equalsIgnoreCase(request.getSlang()));
        if (request.getLastFewDays() != null && request.getLastFewDays() == -1) {
            // 同步全量
            Integer maxId = orderMasterRPCClient.findMaxId();
            int max = maxId.intValue();
            Integer total = max;
            // 每次从数据库取500条数据
            AsyncExecutor.listenableRunAsync(() -> {
                int count = 1;
                while (count < max) {
                    // init bulk request
                    BulkRequest requestAsync = new BulkRequest();
                    requestAsync.setKey(OrderConstant.FULL_KEY);
                    List<OrderMasterDTO> updatedOrderListAsync = orderMasterRPCClient.rangeById(count, count += 500);
                    updatedOrderListAsync = updatedOrderListAsync.stream().filter(s->Objects.nonNull(s) && s.getDeptParentId()==-1).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(updatedOrderListAsync)) {
                        // update data
                        this.updateDeptParentInfo(updatedOrderListAsync);
                        System.out.println("now to id:" + updatedOrderListAsync.get(updatedOrderListAsync.size()-1).getId());
                    }
                }
                logger.info("完成采购父部门数据同步，同步条数：{}", max);
            }).addFailureCallback(throwable -> {
                final Transaction transaction = Cat.newTransaction(CAT_TYPE, "syncFullData");
                transaction.addData("全量同步采购父部门数据失败:" + throwable.getMessage());
                transaction.setStatus(throwable);
                logger.error("全量同步采购父部门数据失败:" + throwable);
                transaction.complete();
            });
        } else if (request.getLastFewDays() != null && request.getLastFewDays() > 0) {
            // 同步几天
            Integer total;
            ZonedDateTime zonedDateTime = LocalDate.now().plusDays(-(request.getLastFewDays())).atStartOfDay().atZone(ZoneId.systemDefault());
            List<Integer> lastUpdatedId = orderMasterRPCClient.findIdByUpdateTimeAfter(Date.from(zonedDateTime.toInstant()));
            total = lastUpdatedId.size();
            AsyncExecutor.listenableRunAsync(() -> {
                List<List<Integer>> lastUpdatedIdPartition = Lists.partition(lastUpdatedId, 500);
                for (List<Integer> updatedIdList : lastUpdatedIdPartition) {
                    List<OrderMasterDTO> updatedOrderList = orderMasterRPCClient.findByOrderIdSet(new HashSet<>(updatedIdList));
                    // update data
                    this.updateDeptParentInfo(updatedOrderList);
                }
            }).addFailureCallback(throwable -> {
                logger.error("手同步{}之后的采购父部门数据失败:" + throwable, zonedDateTime.toLocalDateTime());
                Cat.logError(CAT_TYPE, "syncFullData", "手动同步" + zonedDateTime.toLocalDateTime() + "之后的采购父部门数据失败", throwable);
            });
            logger.info("完成采购父部门数据同步，同步条数：{}", total);
        } else {
            // 其他，报错和提示
            Preconditions.isTrue(false, "此接口只接受全量或n天内的同步，请传入正确的lastFewDays");
        }
        return true;
    }

    /**
     * 更新采购父一级部门信息
     * @param updatedOrderList
     * @return
     */
    private Boolean updateDeptParentInfo(List<OrderMasterDTO> updatedOrderList) {
        List<Integer> deptIdList = updatedOrderList.stream().map(OrderMasterDTO::getFbuydepartmentid).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Integer> deptIdToFindList = New.list();
        // 缓存找学院
        for (Integer deptId : deptIdList) {
            Pair<Integer, String> curParent = deptParentInfoCache.getIfPresent(deptId);
            if (curParent == null) {
                deptIdToFindList.add(deptId);
            }
        }
        // 接口找学院并加入到缓存
        if (CollectionUtils.isNotEmpty(deptIdToFindList)) {
            List<DepartmentDTO> deptList = userRPCClient.findDepartmentByIdList(deptIdToFindList);
            List<Integer> deptParentIdList = deptList.stream().map(DepartmentDTO::getParentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<DepartmentDTO> deptParentList = userRPCClient.findDepartmentByIdList(deptParentIdList);
            Map<Integer, DepartmentDTO> parentIdNameMap = deptParentList.stream().collect(Collectors.toMap(DepartmentDTO::getId, Function.identity(), (ov, nv) -> nv));
            // 缓存存学院(需要串行运行，后续都采用缓存获取）
            deptList.forEach(departmentDTO -> {
                DepartmentDTO parentDept = parentIdNameMap.get(departmentDTO.getParentId());
                if (parentDept != null) {
                    deptParentInfoCache.put(departmentDTO.getId(), Pair.of(parentDept.getId(), parentDept.getName()));
                }
            });
        }

        // update data
        List<UpdateOrderParamDTO> updateList = New.list();
        for (OrderMasterDTO orderMasterDTO : updatedOrderList) {
            UpdateOrderParamDTO updateOrder = new UpdateOrderParamDTO();
            updateOrder.setUpdateTime(orderMasterDTO.getUpdateTime());
            updateOrder.setOrderNo(orderMasterDTO.getForderno());
            Pair<Integer, String> parentIdName = deptParentInfoCache.getIfPresent(orderMasterDTO.getFbuydepartmentid());
            if (parentIdName == null) {
                continue;
            }
            updateOrder.setDeptParentId(parentIdName.getLeft());
            updateOrder.setDeptParentName(parentIdName.getRight());
            updateList.add(updateOrder);
        }
        return orderMasterRPCClient.updateFieldByOrderNo(updateList);
    }

    /**
     * 查询和更新缺失的父部门和其id
     * @param request
     * @return
     */
    public Boolean findAndUpdateParentDept(SyncDataRequestDTO request) {
        Preconditions.isTrue("<EMAIL>".equalsIgnoreCase(request.getSlang()));
        if (request.getLastFewDays() != null && request.getLastFewDays() == -1) {
            // 同步全量
            Integer maxId = orderMasterRPCClient.findMaxId();
            int max = maxId.intValue();
            Integer total = max;
            // 每次从数据库取500条数据
            AsyncExecutor.listenableRunAsync(() -> {
                int count = 1;
                while (count < max) {
                    // init bulk request
                    BulkRequest requestAsync = new BulkRequest();
                    requestAsync.setKey(OrderConstant.FULL_KEY);
                    List<OrderMasterDTO> updatedOrderListAsync = orderMasterRPCClient.rangeById(count, count += 500);
                    // 构造es更新体和更新
                    for (OrderMasterDTO master : updatedOrderListAsync) {
                        if (master.getId() != -1 && master.getDeptParentId() != -1) {
                            Map<String, Object> curUpdateMap = new HashMap<>();
                            curUpdateMap.put("dept_parent_id", master.getDeptParentId());
                            curUpdateMap.put("dept_parent_name", master.getDeptParentName());
                            requestAsync.addUpdateRequest(master.getId().toString(), curUpdateMap);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(updatedOrderListAsync)
                            && CollectionUtils.isNotEmpty(requestAsync.getRequests()))
                    searchAPIClient.bulkUpdate(requestAsync);
                }
                logger.info("完成采购父部门数据同步到es，同步条数：{}", max);
            }).addFailureCallback(throwable -> {
                final Transaction transaction = Cat.newTransaction(CAT_TYPE, "syncFullData");
                transaction.addData("全量同步采购父部门数据到es失败:" + throwable.getMessage());
                transaction.setStatus(throwable);
                logger.error("全量同步采购父部门数据到es失败:" + throwable);
                transaction.complete();
            });
        } else if (request.getLastFewDays() != null && request.getLastFewDays() > 0) {
            // 同步几天
            Integer total;
            ZonedDateTime zonedDateTime = LocalDate.now().plusDays(-(request.getLastFewDays())).atStartOfDay().atZone(ZoneId.systemDefault());
            List<Integer> lastUpdatedId = orderMasterRPCClient.findIdByUpdateTimeAfter(Date.from(zonedDateTime.toInstant()));
            total = lastUpdatedId.size();
            AsyncExecutor.listenableRunAsync(() -> {
                List<List<Integer>> lastUpdatedIdPartition = Lists.partition(lastUpdatedId, 500);
                for (List<Integer> updatedIdList : lastUpdatedIdPartition) {
                    List<OrderMasterDTO> updatedOrderList = orderMasterRPCClient.findByOrderIdSet(new HashSet<>(updatedIdList));
                    // 构造es更新体和更新
                    BulkRequest requestAsync = new BulkRequest();
                    requestAsync.setKey(OrderConstant.FULL_KEY);
                    for (OrderMasterDTO master : updatedOrderList) {
                        if (master.getId() != -1 && master.getDeptParentId() != -1) {
                            Map<String, Object> curUpdateMap = new HashMap<>();
                            curUpdateMap.put("dept_parent_id", master.getDeptParentId());
                            curUpdateMap.put("dept_parent_name", master.getDeptParentName());
                            requestAsync.addUpdateRequest(master.getId().toString(), curUpdateMap);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(updatedOrderList)
                            && CollectionUtils.isNotEmpty(requestAsync.getRequests()))
                        searchAPIClient.bulkUpdate(requestAsync);
                }
            }).addFailureCallback(throwable -> {
                logger.error("手同步{}之后的采购父部门数据到es失败:" + throwable, zonedDateTime.toLocalDateTime());
                Cat.logError(CAT_TYPE, "syncFullData", "手动同步" + zonedDateTime.toLocalDateTime() + "之后的采购父部门数据失败", throwable);
            });
            logger.info("完成采购父部门数据同步到es，同步条数：{}", total);
        } else {
            // 其他，报错和提示
            Preconditions.isTrue(false, "此接口只接受全量或n天内的同步，请传入正确的lastFewDays");
        }
        return true;
    }
}
