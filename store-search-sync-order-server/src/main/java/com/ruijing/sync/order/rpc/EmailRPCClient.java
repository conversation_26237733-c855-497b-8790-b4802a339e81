package com.ruijing.sync.order.rpc;

import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.message.api.dto.MessageDTO;
import com.ruijing.message.api.enums.MessageTypeEnum;
import com.ruijing.message.api.service.MessageService;
import com.ruijing.sync.order.utils.ExcelUtils;

import java.io.File;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2021/10/13 10:17
 */
@ServiceClient
public class EmailRPCClient {

    private static final String CAT_TYPE = "EmailRPCClient";

    @MSharpReference(remoteAppkey = "msharp-message-service")
    private MessageService messageService;

    /**
     * 异步发送信息
     * @param messageDTO
     */
    public Boolean sendEmail(MessageDTO messageDTO) {
        try {
            messageService.asyncSend(messageDTO);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "sendEmail", "异步发送邮件失败", e);
            throw e;
        }
        return true;
    }

    /**
     * 发送excel给邮件接收人
     * @param emailArray
     * @param tempAbnormalFile
     * @param emailTitle
     * @return
     */
    public Boolean sendFileToEmail(String[] emailArray, File tempAbnormalFile, String emailTitle) {
        byte[] bytes = ExcelUtils.getBytesFromFile(tempAbnormalFile);
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setReceiver(emailArray);
        messageDTO.setSubject(emailTitle);
        messageDTO.setContent("Mysql与ES数据的差异，请查收附件");
        messageDTO.setMessageType(MessageTypeEnum.EMAIL_HTML);
        messageDTO.setAttachment(bytes);
        messageDTO.setAttachmentName(emailTitle);
        return this.sendEmail(messageDTO);
    }
}
