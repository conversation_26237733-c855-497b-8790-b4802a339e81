package com.ruijing.sync.order.utils;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.ruijing.fundamental.lang.Preconditions;

import java.util.concurrent.TimeUnit;

/**
 * @Author: <PERSON><PERSON>
 * @Description:
 * @DateTime: 2021/10/13 14:39
 */
public class CacheUtils {

    /**
     * 用来存缓存的map，主要用于存放防止重入的方法
     */
    private static Cache<String, Object> cacheMap = CacheBuilder.newBuilder().maximumSize(50).expireAfterWrite(24, TimeUnit.HOURS).build();

    /**
     * 控制重入
     * @param uniqKey
     */
    public static synchronized void controlRepeatOperation(String uniqKey) {
        Object curMethod = cacheMap.getIfPresent(uniqKey);
        if (curMethod == null) {
            cacheMap.put(uniqKey, (Object)uniqKey);
            return;
        }
        Preconditions.isTrue(false, uniqKey+"已存在，请等待前述操作完成再点击操作");
    }

    /**
     * 根据key删除缓存
     * @param uniqKey
     * @return
     */
    public static boolean removeCache(String uniqKey) {
        try {
            cacheMap.invalidate(uniqKey);
        } catch (Exception e) {
            Preconditions.isTrue(false, uniqKey+"方法的重入缓存无法清除，请技术查看和处理。");
        }
        return true;
    }
}
