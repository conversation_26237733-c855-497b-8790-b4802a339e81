package com.ruijing.sync.order;

import com.ruijing.cat.springboot.autoconfigure.annotation.EnableCat;
import com.ruijing.data.sync.springboot.autoconfigure.annotation.EnableDataSync;
import com.ruijing.fundamental.springboot.starter.ServiceBootApplication;
import com.ruijing.pearl.annotation.EnablePearl;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * <AUTHOR>
 * @date 2019/7/22 17:57
 */
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnablePearl
@EnableDataSync
@EnableCat
public class SyncOrderApplication {
    public static void main(String[] args) {
        ServiceBootApplication.main(SyncOrderApplication.class, args);
    }
}