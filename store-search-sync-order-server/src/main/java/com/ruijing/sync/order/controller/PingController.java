package com.ruijing.sync.order.controller;

import com.ruijing.fundamental.remoting.msharp.service.HeartBeatService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2019/6/22 17:57
 */
@RestController
public class PingController implements HeartBeatService {

    @RequestMapping("/ping")
    @ResponseBody
    public String ping() {
        return "pong";
    }
}
