package com.ruijing.sync.order.rpc;

import com.google.common.collect.Lists;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.service.DepartmentRpcService;
import com.ruijing.store.user.api.service.UserInfoService;
import com.ruijing.sync.order.log.annotation.ServiceLog;
import com.ruijing.sync.order.log.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 用户信息RPC客户端
 * @author: zhongyulei
 * @create: 2020/10/20 17:49
 **/
@ServiceClient
@CatAnnotation
public class UserRPCClient {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "UserClient";

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserInfoService userInfoService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private DepartmentRpcService departmentRpcService;

    /**
     * 根据 userId 和 orgId 查询用户信息
     * @param userIdList
     * @param orgId
     * @return
     */
    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public List<UserBaseInfoDTO> getUserByIdsAndOrgId(Set<Integer> userIdList, Integer orgId) {
        if (CollectionUtils.isEmpty(userIdList) || orgId == null) {
            return Collections.emptyList();
        }

        Transaction transaction = Cat.newTransaction(CAT_TYPE, "getUserByIdsAndOrgId");
        RemoteResponse<List<UserBaseInfoDTO>> response = null;
        try {
            response = userInfoService.getUserByIdsAndOrgId(new ArrayList<>(userIdList), orgId);
            transaction.setSuccess();
        } catch (Exception e) {
            logger.error("根据医院id批量查询部门信息getUserByIdsAndOrgId异常{}", e);
            transaction.addData(CatUtils.buildStackInfo("根据医院id批量查询部门信息getUserByIdsAndOrgId异常", e));
            throw new IllegalStateException("根据医院id批量用户信息异常");
        } finally {
            transaction.complete();
        }

        if (response == null || !response.isSuccess()) {
            String responseLog = JsonUtils.toJson(response);
            logger.error("根据医院id批量查询部门信息getUserByIdsAndOrgId失败{}", responseLog);
            Cat.logWarn(CAT_TYPE, "getUserByIdsAndOrgId", "根据医院id批量用户信息失败" + responseLog);
            return New.emptyList();
        }

        return response.getData();
    }

    @ServiceLog(description = "根据id查询部门信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<DepartmentDTO> findDepartmentByIdList(List<Integer> departmentIdList) {
        List<Long> departmentParams = departmentIdList.stream().map(Integer::longValue).collect(Collectors.toList());
        // RPC接口有限制单次200个
        List<List<Long>> partition = Lists.partition(departmentParams, 200);
        List<DepartmentDTO> result = new ArrayList<>(departmentIdList.size());

        for (List<Long> departmentIdPartition : partition) {
            RemoteResponse<List<DepartmentDTO>> response = departmentRpcService.getDepartmentsByIds(new ArrayList<>(departmentIdPartition));
            Preconditions.isTrue(response.isSuccess(), "根据id查询部门信息失败！" + JsonUtils.toJsonIgnoreNull(response));
            result.addAll(response.getData());
        }
        return result;
    }

    public DepartmentDTO findDepartmentId(Integer departmentId) {
        List<DepartmentDTO> departmentByIdList = this.findDepartmentByIdList(Arrays.asList(departmentId));
        if (CollectionUtils.isEmpty(departmentByIdList)) {
            return null;
        }

        return departmentByIdList.get(0);
    }

    public DepartmentDTO findParentDepartmentById(Integer departmentId) {
        DepartmentDTO department = this.findDepartmentId(departmentId);
        if (department == null) {
            return null;
        }

        Integer parentId = department.getParentId();
        return this.findDepartmentId(parentId);
    }
}
