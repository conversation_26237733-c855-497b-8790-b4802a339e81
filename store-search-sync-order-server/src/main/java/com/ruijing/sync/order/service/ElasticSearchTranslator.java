package com.ruijing.sync.order.service;

import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class ElasticSearchTranslator {
    /**
     * 组装订单经费卡字段
     * @param orderFundCardList
     * @return
     */
    public static List<Map<String, Object>> parseOrderCardField(List<RefFundcardOrderDTO> orderFundCardList) {
        if (CollectionUtils.isEmpty(orderFundCardList)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> result = new ArrayList<>();
        for (RefFundcardOrderDTO item : orderFundCardList) {
            Map<String, Object> map = new HashMap<>();
            map.put("card_id", item.getCardId());
            map.put("card_no", item.getCardNo());
            map.put("campus_code", item.getCampusCode());
            map.put("campus_name", item.getCampusName());
            map.put("fund_type", item.getFundType());
            map.put("last_level_card_id", getLastLevelCardId(item));
            result.add(map);
        }

        return result;
    }

    /**
     * 获取最后一级经费卡id，有subjectId取subjectId,否则取cardId，兼容这个三级卡和非三级卡卡id字段不一致的坑
     * @param card 卡对象
     * @return 卡id
     */
    public static String getLastLevelCardId(RefFundcardOrderDTO card){
        return StringUtils.isNotEmpty(card.getSubjectId()) ? card.getSubjectId() : card.getCardId();
    }

    /**
     * 组装订单审批日志字段
     * @param approvalLogList
     * @return
     */
    public static List<Map<String, Object>> parseApprovalLogField(List<OrderApprovalLogDTO> approvalLogList) {
        if (CollectionUtils.isEmpty(approvalLogList)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> result = new ArrayList<>();
        for (OrderApprovalLogDTO item : approvalLogList) {
            Map<String, Object> map = new HashMap<>();
            map.put("log_id", item.getId());
            map.put("approve_status", item.getApproveStatus());
            map.put("operator_id", item.getOperatorId());
            map.put("creation_time", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, item.getCreationTime()));
            result.add(map);
        }

        return result;
    }

    /**
     * 组装订单额外信息字段
     * @param orderExtraDTOList
     * @return
     */
    public static List<Map<String, Object>> parseOrderExtraField(List<OrderExtraDTO> orderExtraDTOList) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (OrderExtraDTO orderExtra : orderExtraDTOList) {
            OrderExtraEnum orderExtraEnum;
            if((orderExtraEnum = OrderExtraEnum.getByValue(orderExtra.getExtraKey())) != null && !orderExtraEnum.getNeedSyncToSearch()){
                // 跳过不需要同步的order_extra。空的话，就是这个项目没重新发版，这时候当成需要同步
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("extra_id", orderExtra.getId());
            map.put("extra_key", orderExtra.getExtraKey());
            map.put("extra_value", ElasticSearchTranslator.parseOrderExtraValue(orderExtra.getExtraKey(), orderExtra.getExtraValue()));
            result.add(map);
        }
        return result;
    }

    /**
     * 转换orderExtra的值
     * @param extraKey extraKey
     * @param value 值
     * @return 转换后的对象
     */
    public static Object parseOrderExtraValue(Integer extraKey, String value) {
        OrderExtraEnum orderExtraEnum = OrderExtraEnum.getByValue(extraKey);
        if(orderExtraEnum == null){
            return value;
        }
        Class<?> clazz = orderExtraEnum.getDataTypeClass();
        if(clazz == String.class){
            return value;
        }
        return JsonUtils.fromJson(value, clazz);
    }

    /**
     * 组装搜索订单详情字段
     * @param orderDetailDTOList
     * @return
     */
    public static List<Map<String, Object>> parseOrderDetailField(List<OrderDetailDTO> orderDetailDTOList) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (OrderDetailDTO detail : orderDetailDTOList) {
            Map<String, Object> map = new HashMap<>();
            map.put("detail_id", detail.getId());
            map.put("categoryID", detail.getCategoryid());
            map.put("category_directory_id", detail.getCategoryDirectoryId());
            map.put("fgoodcode", detail.getFgoodcode());
            map.put("product_code", detail.getProductCode());
            map.put("fbrandid", detail.getFbrandid());
            map.put("fbrand", detail.getFbrand());
            map.put("fgoodname", detail.getFgoodname());
            map.put("fspec", detail.getFspec());
            map.put("funit", detail.getFunit());
            map.put("fquantity", detail.getFquantity().doubleValue());
            map.put("return_status", detail.getReturnStatus());
            map.put("fbidprice", detail.getFbidprice().doubleValue());
            map.put("original_price", detail.getOriginalPrice().doubleValue());
            map.put("original_amount", detail.getOriginalAmount().doubleValue());
            map.put("product_id", detail.getProductSn());
            map.put("fcancelquantity", detail.getFcancelquantity().doubleValue());
            map.put("fbidamount", detail.getFbidamount().doubleValue());
            map.put("fclassification", detail.getFclassification());
            map.put("dangerous_type_name", detail.getDangerousTypeName());
            map.put("dangerous_type", detail.getDangerousTypeId());
            map.put("regulatory_type", detail.getRegulatoryTypeId());
            map.put("cas_no", detail.getCasno());
            map.put("regulatory_type_name", detail.getRegulatoryTypeName());
            map.put("first_category_id", detail.getFirstCategoryId());
            map.put("first_category_name", detail.getFirstCategoryName());
            map.put("second_category_id", detail.getSecondCategoryId());
            map.put("second_category_name", detail.getSecondCategoryName());
            map.put("category_tag", detail.getCategoryTag());
            map.put("fee_type_tag", detail.getFeeTypeTag());
            map.put("supp_id", detail.getSuppId());
            Boolean isModifyPrice = detail.getModifyPrice();
            if (isModifyPrice == null) {
                map.put("modify_price", 0);
            } else {
                map.put("modify_price", isModifyPrice ? 1 : 0);
            }
            map.put("fpicpath", detail.getFpicpath());

            result.add(map);
        }
        return result;
    }

    /**
     * 组装搜索订单详情字段
     * @param orderDetailItem
     * @return
     */
    public static List<Map<String, Object>> parseDMLOrderDetailField(List<Map<String, Object>> orderDetailItem) {
        List<Map<String, Object>> result = new ArrayList<>(orderDetailItem.size());
        for (Map<String, Object> detail : orderDetailItem) {
            Map<String, Object> map = new HashMap<>();
            map.put("detail_id", detail.get("id"));
            map.put("categoryID", detail.get("CategoryID"));
            map.put("category_directory_id", detail.get("category_directory_id"));
            map.put("fgoodcode", detail.get("fgoodcode"));
            map.put("fbrandid", detail.get("fbrandid"));
            map.put("fbrand", detail.get("fbrand"));
            map.put("fgoodname", detail.get("fgoodname"));
            map.put("fspec", detail.get("fspec"));
            map.put("funit", detail.get("funit"));
            map.put("fquantity", detail.get("fquantity"));
            map.put("return_status", detail.get("return_status"));
            map.put("fbidprice", detail.get("fbidprice"));
            map.put("original_price", detail.get("original_price"));
            map.put("original_amount", detail.get("original_amount"));
            map.put("product_id", detail.get("product_sn"));
            map.put("fcancelquantity", detail.get("fcancelquantity"));
            map.put("fbidamount", detail.get("fbidamount"));
            map.put("fclassification", detail.get("fclassification"));
            map.put("dangerous_type_name", detail.get("dangerous_type_name"));
            map.put("dangerous_type", detail.get("dangerous_type_id"));
            map.put("regulatory_type", detail.get("regulatory_type_id"));
            map.put("cas_no", detail.get("casno"));
            map.put("regulatory_type_name", detail.get("regulatory_type_name"));
            map.put("first_category_id", detail.get("first_category_id"));
            map.put("first_category_name", detail.get("first_category_name"));
            map.put("second_category_id", detail.get("second_category_id"));
            map.put("second_category_name", detail.get("second_category_name"));
            map.put("category_tag", detail.get("category_tag"));
            map.put("fee_type_tag", detail.get("fee_type_tag"));
            map.put("supp_id", detail.get("supp_id"));
            map.put("modify_price", detail.get("modify_price"));
            map.put("fpicpath", detail.get("fpicpath"));

            result.add(map);
        }
        return result;
    }

    /**
     * 更新订单主表搜索字段
     * @param dmlData
     * @return
     */
    public static Map<String, Object> parseOrderMasterField(Map<String, Object> dmlData) {
        // 因为外部有另外设值，以防有相同key，让同样key自旋插入（无所谓覆盖）
        Map<String, Object> map = new HashMap<>(dmlData.size());
        map.put("id", dmlData.get("id"));
        map.put("bid_order_id", dmlData.get("bid_order_id"));
        map.put("fund_status", dmlData.get("fund_status"));
        map.put("forderno", dmlData.get("forderno"));
        map.put("ftbuyappid", dmlData.get("ftbuyappid"));
        map.put("fbuydepartment", dmlData.get("fbuydepartment"));
        map.put("fbuydepartmentid", dmlData.get("fbuydepartmentid"));
        map.put("fsuppid", dmlData.get("fsuppid"));
        map.put("fsuppname", dmlData.get("fsuppname"));
        map.put("forderamounttotal", dmlData.get("forderamounttotal"));
        map.put("order_type", dmlData.get("order_type"));
        map.put("fuserid", dmlData.get("fuserid"));
        map.put("species", dmlData.get("species"));
        map.put("forderdate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, (Date) dmlData.get("forderdate")));
        if (dmlData.get("fdeliverydate") != null) {
            map.put("fdeliverydate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, (Date) dmlData.get("fdeliverydate")));
        }
        map.put("status", dmlData.get("status"));
        map.put("fbuyername", dmlData.get("fbuyername"));
        map.put("projectNumber", dmlData.get("projectNumber"));
        map.put("fbuyercontactman", dmlData.get("fbuyercontactman"));
        map.put("fbiderdeliveryplace", dmlData.get("fbiderdeliveryplace"));
        map.put("fusername", dmlData.get("fusername"));
        map.put("fbuyerid", dmlData.get("fbuyerid"));
        map.put("statement_id", dmlData.get("statement_id"));
        map.put("update_time", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, (Date) dmlData.get("update_time")));
        map.put("return_amount", dmlData.get("return_amount"));
        if (dmlData.get("fconfirmdate") != null) {
            map.put("fconfirmdate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, (Date) dmlData.get("fconfirmdate")));
        }
        map.put("fbuyertelephone", dmlData.get("fbuyertelephone"));
        if (dmlData.get("flastreceivedate") != null) {
            map.put("flastreceivedate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, (Date)dmlData.get("flastreceivedate")));
        }
        map.put("flastreceiveman", dmlData.get("flastreceiveman"));
        if (dmlData.get("fcanceldate") != null) {
            map.put("fcanceldate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, (Date) dmlData.get("fcanceldate")));
        }
        map.put("create_time", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, (Date) dmlData.get("create_time")));
        map.put("inventory_status", dmlData.get("inventory_status"));
        map.put("carry_fee", dmlData.get("carry_fee"));
        map.put("flastreceivemanid", dmlData.get("flastreceivemanid"));
        map.put("flow_id", dmlData.get("flow_id"));
        map.put("statement_status", dmlData.get("statement_status"));
        map.put("dept_parent_id", dmlData.get("dept_parent_id"));
        map.put("dept_parent_name", dmlData.get("dept_parent_name"));
        if (dmlData.get("in_state_time") != null) {
            map.put("in_state_time", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, (Date)dmlData.get("in_state_time")));
        }
        return map;
    }

    /**
     * 解析订单主表数据为搜索map对象
     * @param byOrderIdSet
     * @return
     */
    public static List<Map<String, Object>> parseOrderMasterDTOSkip(Set<Integer> byOrderIdSet) {
        List<Map<String, Object>> mapList = new ArrayList<>(byOrderIdSet.size());

        byOrderIdSet.stream().forEach(orderId -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", orderId);
            mapList.add(map);
        });
        return mapList;
    }

    /**
     * 解析订单主表数据为搜索map对象
     * @param byOrderIdSet
     * @return
     */
    public static List<Map<String, Object>> parseOrderMasterDTO(List<OrderMasterDTO> byOrderIdSet) {
        List<Map<String, Object>> mapList = new ArrayList<>(byOrderIdSet.size());

        byOrderIdSet.stream().forEach(master -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", master.getId());
            map.put("bid_order_id", master.getBidOrderId());
            map.put("fund_status", master.getFundStatus());
            map.put("forderno", master.getForderno());
            map.put("ftbuyappid", master.getFtbuyappid());
            map.put("fbuydepartment", master.getFbuydepartment());
            map.put("fbuydepartmentid", master.getFbuydepartmentid());
            map.put("fsuppid", master.getFsuppid());
            map.put("fsuppname", master.getFsuppname());
            map.put("forderamounttotal", master.getForderamounttotal());
            map.put("order_type", master.getOrderType());
            map.put("fuserid", master.getFuserid());
            map.put("species", master.getSpecies());
            map.put("forderdate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, master.getForderdate()));
            //TODO: 需要增加create time，配合store order 更新
            map.put("create_time", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, master.getCreateTime()));
            if (master.getFdeliverydate() != null) {
                map.put("fdeliverydate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, master.getFdeliverydate()));
            }
            map.put("status", master.getStatus());
            map.put("fbuyername", master.getFbuyername());
            map.put("projectNumber", master.getProjectnumber());
            map.put("fbuyercontactman", master.getFbuyercontactman());
            map.put("fbiderdeliveryplace", master.getFbiderdeliveryplace());
            map.put("fusername", master.getFusername());
            map.put("fbuyerid", master.getFbuyerid());
            map.put("statement_id", master.getStatementId());
            map.put("update_time", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, master.getUpdateTime()));
            map.put("return_amount", master.getReturnAmount());
            if (master.getFconfirmdate() != null) {
                map.put("fconfirmdate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, master.getFconfirmdate()));
            }
            map.put("fbuyertelephone", master.getFbuyertelephone());
            if (master.getFlastreceivedate() != null) {
                map.put("flastreceivedate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, master.getFlastreceivedate()));
            }
            map.put("flastreceiveman", master.getFlastreceiveman());
            if (master.getFcanceldate() != null) {
                map.put("fcanceldate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, master.getFcanceldate()));
            }
            map.put("inventory_status", master.getInventoryStatus());
            map.put("carry_fee", master.getCarryFee());
            map.put("flastreceivemanid", master.getFlastreceivemanid());
            map.put("flow_id", master.getFlowId());
            map.put("statement_status", master.getStatementStatus());
            map.put("dept_parent_id", master.getDeptParentId());
            map.put("dept_parent_name", master.getDeptParentName());
            if (master.getInStateTime() != null) {
                map.put("in_state_time", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, master.getInStateTime()));
            }
            mapList.add(map);
        });
        return mapList;
    }

}
