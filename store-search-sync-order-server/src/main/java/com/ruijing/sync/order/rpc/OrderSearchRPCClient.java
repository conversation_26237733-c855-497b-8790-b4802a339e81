package com.ruijing.sync.order.rpc;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.FieldRangeDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.api.search.service.OrderSearchRpcService;

import java.util.List;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2021/10/12 14:39
 */
@ServiceClient
public class OrderSearchRPCClient {

    @MSharpReference(remoteAppkey = "store-order-service")
    private OrderSearchRpcService orderSearchRpcService;

    /**
     * 通过es 范围id获取订单列表
     * @param lower
     * @param higher
     * @return
     */
    public List<OrderMasterSearchDTO> getMasterSearchByIdRange(Integer lower, Integer higher) {
        Preconditions.isTrue(lower < higher);
        FieldRangeDTO fieldRangeDTO = new FieldRangeDTO();
        fieldRangeDTO.setField("id");
        fieldRangeDTO.setLower(lower.toString());
        fieldRangeDTO.setUpper(higher.toString());
        // 为了保持与mysql的查询一致
        fieldRangeDTO.setIncludeLower(false);
        fieldRangeDTO.setIncludeUpper(true);

        OrderSearchParamDTO param = new OrderSearchParamDTO();
        param.setFieldRangeList(New.list(fieldRangeDTO));
        param.setPageSize(500);
        RemoteResponse<SearchPageResultDTO<OrderMasterSearchDTO>> response = orderSearchRpcService.commonSearch(param);
        Preconditions.isTrue(response.isSuccess(), "按照订单范围在es查询订单失败，请检查语句");
        SearchPageResultDTO<OrderMasterSearchDTO> pageRes = response.getData();
        if (pageRes != null) {
            return pageRes.getRecordList();
        } else {
            return New.list();
        }
    }

    /**
     * 通过订单id列表查找es数据
     * @param orderIdList
     * @return
     */
    public List<OrderMasterSearchDTO> getOrderByIdList(List<Integer> orderIdList) {
        OrderSearchParamDTO param = new OrderSearchParamDTO();
        param.setOrderIdList(New.list(orderIdList));
        RemoteResponse<List<OrderMasterSearchDTO>> response = orderSearchRpcService.searchOrderByIdList(param);
        Preconditions.isTrue(response.isSuccess(), "通过id列表搜索订单服务有误，" + response.getMsg());
        return response.getData();
    }

    /**
     * 通过订单号查找es数据
     * @param orderNoList
     * @return
     */
    public List<OrderMasterSearchDTO> getOrderByOrderNoList(List<String> orderNoList) {
        OrderSearchParamDTO param = new OrderSearchParamDTO();
        param.setOrderNoList(orderNoList);
        RemoteResponse<SearchPageResultDTO<OrderMasterSearchDTO>> response = orderSearchRpcService.commonSearch(param);
        Preconditions.isTrue(response.isSuccess(), "通过id列表搜索订单服务有误，" + response.getMsg());
        SearchPageResultDTO<OrderMasterSearchDTO> searchRes = response.getData();
        if (searchRes == null) {
            return New.list();
        }
        return searchRes.getRecordList();
    }
}
