package com.ruijing.sync.order.constant;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;

import java.util.HashSet;
import java.util.Set;

/**
 * 本地化部署监听binLog同步的单位
 */
public class OrganizationConstant {

    /**
     * 广东医科大学
     */
    public static final String GUANG_DONG_YI_KE_DA_XUE = "GUANG_DONG_YI_KE_DA_XUE";

    /**
     * 陆军军医
     */
    public static final String LU_JUN_JUN_YI_DA_XUE = "LU_JUN_JUN_YI_DA_XUE";

    /**
     * 中大
     */
    public static final String ZHONG_SHAN_DA_XUE = "ZHONG_SHAN_DA_XUE";

    /**
     * 中大深圳
     */
    public static final String ZHONG_SHAN_DA_XUE_SHEN_ZHEN = "ZHONG_SHAN_DA_XUE_SHEN_ZHEN";

    public static final Set<String> LOCAL_DEPLOY_ORG_SET = new HashSet<String>() {{
        add(GUANG_DONG_YI_KE_DA_XUE);
        add(LU_JUN_JUN_YI_DA_XUE);
        add(ZHONG_SHAN_DA_XUE);
        add(ZHONG_SHAN_DA_XUE_SHEN_ZHEN);
        add(OrgConst.HUA_QIAO_DA_XUE);
        add(OrgConst.CHNEG_DU_YI_XUE_YUAN);
        add(OrgConst.QING_HUA_DA_XUE_GU_JI_YAN_JIU_SHENG_YUAN);
        add(OrgEnum.QING_HUA_DA_XUE_YAN_JIU_SHENG_YUAN_XIAO_E.getCode());
    }};

}
