package com.ruijing.sync.order.rpc;

import com.google.common.collect.Lists;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.docking.dto.DockingRequestDTO;
import com.ruijing.store.order.api.base.docking.service.DockingExtraRpcService;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.service.OrderExtraRpcService;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterCommonReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderMasterCommonService;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.api.base.other.dto.OrderConfirmForTheRecordDTO;
import com.ruijing.store.order.api.base.other.dto.OrderRemarkDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.api.base.other.service.OrderRelatedRPCService;
import com.ruijing.sync.order.log.annotation.ServiceLog;
import com.ruijing.sync.order.log.enums.ServiceType;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2020/10/19 15:41
 **/
@ServiceClient
@CatAnnotation
public class OrderMasterRPCClient {

    @MSharpReference(remoteAppkey = "store-order-service")
    private OrderMasterCommonService orderMasterCommonService;

    @MSharpReference(remoteAppkey = "store-order-service")
    private OrderRelatedRPCService orderRelatedRPCService;

    @MSharpReference(remoteAppkey = "store-order-service")
    private DockingExtraRpcService dockingExtraRpcService;

    @MSharpReference(remoteAppkey = "store-order-service")
    private OrderExtraRpcService orderExtraRpcService;

    private static final String CAT_TYPE = "OrderMasterRPCClient";

    /**
     * 通过订单id 查询订单数据
     * @param request
     * @return
     */
    public OrderMasterDTO findById(OrderMasterCommonReqDTO request) {

        try {
            RemoteResponse<OrderMasterDTO> response = orderMasterCommonService.findOrderMasterById(request);
            Preconditions.isTrue(response.isSuccess(), "获取订单数据失败！" + JsonUtils.toJsonIgnoreNull(request));
            return response.getData();
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "findById", "获取订单数据失败！", e);
            throw new IllegalStateException("获取订单数据失败！");
        }
    }

    /**
     * 通过采购单id和供应商id查询买家留言信息
     * @param request
     * @return
     */
    public List<OrderRemarkDTO> findByAppIdAndSupplierId(List<OrderRemarkDTO> request) {
        try {
            RemoteResponse<List<OrderRemarkDTO>> response = orderRelatedRPCService.findOrderRemarkByPrimaryKeys(request);
            Preconditions.isTrue(response.isSuccess(), "获取订单买家留言数据失败！");
            return response.getData();
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "findByAppIdAndSupplierId", "获取订单买家留言数据失败！", e);
            throw new IllegalStateException("获取订单买家留言数据失败！");
        }
    }

    @ServiceLog(description = "根据订单号查询单号对接信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<DockingExtraDTO> findDockingListByOrderNo(DockingRequestDTO request) {
        RemoteResponse<List<DockingExtraDTO>> response = dockingExtraRpcService.findDockingExtraByInfoList(request);
        Preconditions.isTrue(response.isSuccess(), "根据订单号查询单号对接信息失败！" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    public List<DockingExtraDTO> findDockingListByOrderNo(List<String> orderNoList) {
        DockingRequestDTO request = new DockingRequestDTO();
        request.setInfoList(orderNoList);

        return this.findDockingListByOrderNo(request);
    }

    @ServiceLog(description = "查询订单审批日志", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderApprovalLogDTO> findApprovalLogByOrderIdList(OrderApprovalRequestDTO request) {
        RemoteResponse<List<OrderApprovalLogDTO>> response = orderRelatedRPCService.findByOrderIdListAndStatus(request);
        Preconditions.isTrue(response.isSuccess(), "查询订单审批日志失败！" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    public List<OrderApprovalLogDTO> findApprovalLogByOrderIdList(List<Integer> orderIdList) {
        OrderApprovalRequestDTO request = new OrderApprovalRequestDTO();
        request.setOrderIdList(orderIdList);

        return this.findApprovalLogByOrderIdList(request);
    }

    @ServiceLog(description = "查询订单绑卡记录", serviceType = ServiceType.COMMON_SERVICE)
    public List<RefFundcardOrderDTO> findCardByOrderIdList(List<Integer> orderIdList) {
        List<RefFundcardOrderDTO> result = new ArrayList<>(orderIdList.size());
        List<List<Integer>> partition = Lists.partition(orderIdList, 300);
        for (List<Integer> orderIdListPartition : partition) {
            RemoteResponse<List<RefFundcardOrderDTO>> response = orderRelatedRPCService.findRefFundCardOrderByOrderIdList(new ArrayList<>(orderIdListPartition));
            Preconditions.isTrue(response.isSuccess(), "查询订单绑卡记录失败！" + JsonUtils.toJsonIgnoreNull(response));
            result.addAll(response.getData());
        }

        return result;
    }

    @ServiceLog(description = "查询订单备案记录", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderConfirmForTheRecordDTO> findOrderConfirmByOrderIdList(List<Integer> orderIdList) {
        OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
        request.setOrderMasterIds(orderIdList);
        RemoteResponse<List<OrderConfirmForTheRecordDTO>> response = orderRelatedRPCService.findOrderConfirmByOrderIdList(request);

        Preconditions.isTrue(response.isSuccess(), "查询订单备案记录失败！" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();

    }

    @ServiceLog(description = "查询订单信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderMasterDTO> findByOrderIdSet(Set<Integer> orderIdSet) {
        OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
        request.setOrderMasterIds(new ArrayList<>(orderIdSet));
        RemoteResponse<List<OrderMasterDTO>> response = orderMasterCommonService.findOrderListByIds(request);
        Preconditions.isTrue(response.isSuccess(), "查询订单信息失败！" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "订单号查询订单信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderMasterDTO> findByOrderOrderNoList(List<String> orderNoList) {
        OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
        request.setOrderMasterNoList(orderNoList);
        RemoteResponse<List<OrderMasterDTO>> response = orderMasterCommonService.findOrderMasterByOrderNoList(request);
        Preconditions.isTrue(response.isSuccess(), "查询订单信息失败！" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "采购单id查询订单信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderMasterDTO> findOrderByApplicationIdList(List<Integer> applicationIdList) {
        OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
        request.setApplicationIdList(applicationIdList);
        RemoteResponse<List<OrderMasterDTO>> response = orderMasterCommonService.findOrderByApplicationIdList(request);
        Preconditions.isTrue(response.isSuccess(), "采购单id查询订单信息失败！" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "查询最大订单id接口", serviceType = ServiceType.COMMON_SERVICE)
    public Integer findMaxId() {
        RemoteResponse<Integer> response = orderMasterCommonService.findMaxOrderId();
        Preconditions.isTrue(response.isSuccess(), "查询最大订单id接口异常");

        return response.getData();
    }

    @ServiceLog(description = "根据id分页查询订单数据", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderMasterDTO> rangeById(Integer left, Integer right) {
        OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
        request.setLeftInterval(left);
        request.setRightInterval(right);

        RemoteResponse<List<OrderMasterDTO>> response = orderMasterCommonService.rangeById(request);
        Preconditions.isTrue(response.isSuccess(), "查询最大订单id接口异常");

        return response.getData();
    }

    @ServiceLog(description = "查询最近日期内更新的订单id数组", serviceType = ServiceType.COMMON_SERVICE)
    public List<Integer> findIdByUpdateTimeAfter(Date lastUpdatedDate) {
        OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
        request.setUpdateTime(lastUpdatedDate);
        RemoteResponse<List<Integer>> response = orderMasterCommonService.findIdByUpdateTimeAfter(request);
        Preconditions.isTrue(response.isSuccess(), "查询最近日期内更新的订单id数组失败");

        return response.getData();
    }

    @ServiceLog(description = "查询订单额外信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderExtraDTO> findOrderExtraByOrderId(List<Integer> orderIdList) {
        List<List<Integer>> partition = Lists.partition(orderIdList, 300);
        List<OrderExtraDTO> resList = New.list();
        for (List<Integer> part : partition) {
            RemoteResponse<List<OrderExtraDTO>> response = orderExtraRpcService.selectByOrderIdIn(New.list(part));
            String errorMsg = response == null ? "" : response.getMsg();
            Preconditions.isTrue(response != null && response.isSuccess(), "获取订单额外信息失败(" + errorMsg + ")，orderId=" + orderIdList);
            resList.addAll(response.getData());
        }
        return resList;
    }

    /**
     * 根据订单号批量更新订单主表信息，注意带上update time！
     * @param updateList
     * @return
     */
    @ServiceLog(description = "根据订单号批量更新订单主表信息", serviceType = ServiceType.COMMON_SERVICE)
    public Boolean updateFieldByOrderNo(List<UpdateOrderParamDTO> updateList) {
        List<List<UpdateOrderParamDTO>> partition = Lists.partition(updateList, 100);
        RemoteResponse<Integer> response;
        for (List<UpdateOrderParamDTO> part : partition) {
            response = orderMasterCommonService.updateFieldByOrderNo(New.list(part));
            String probErrMsg = response == null ? "" : response.getMsg();
            Preconditions.isTrue(response != null && response.isSuccess(), "根据订单号批量更新订单主表信息失败，msg=" + probErrMsg);
        }
        return true;
    }
}
