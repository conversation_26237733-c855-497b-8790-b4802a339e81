curl -H "Content-Type: application/json" -XPUT 'http://192.168.2.217:9200/t_order_test_20200427' -d'
{
  "settings": {
    "index.store.preload": ["nvd", "dvd"],
    "number_of_replicas": 1,
    "number_of_shards": 3,
    "analysis": {
      "analyzer": {
        "my_simple": {
          "tokenizer": "my_simple"
        }
      },
      "tokenizer": {
        "my_simple": {
           "type": "ngram",
          "min_gram": 13,
          "max_gram": 64,
          "token_chars": [
            "letter",
            "digit"
          ]
        }
      }
    }
  },
  "mappings": {
    "doc": {
      "properties": {
        "id": {
          "type": "long"
        },
       "fbidid": {
          "type": "keyword",
          "doc_values": false
        },
        "forderno": {
          "type": "text",
          "analyzer": "my_simple",
          "doc_values": false
        },
       "fund_status": {
          "type": "keyword",
          "doc_values": false
        },
       "ftbuyappid": {
          "type": "keyword",
          "index": true,
          "doc_values": false
        },
      "fbuydepartment": {
          "type": "text",
          "analyzer": "ik_max_word",
          "doc_values": false
        },
        "fbuydepartmentid": {
          "type": "keyword"
        },
       "fsuppid": {
          "type": "keyword"
        },
       "fsuppname": {
          "type": "text",
          "analyzer": "ik_max_word",
          "doc_values": false
        },
        "forderamounttotal": {
          "type": "scaled_float",
           "scaling_factor": 100,
            "index": false
        },
        "order_type": {
          "type": "keyword"
        },
        "fuserid": {
          "type": "keyword"
        },
        "species": {
          "type": "keyword",
          "doc_values": false
        },
        "forderdate": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss"
        },
        "fdeliverydate": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss"
        },
        "fconfirmdate": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss"
         },
        "status": {
          "type": "keyword"
        },
        "fbuyertelephone": {
            "type": "keyword",
             "index": false,
             "doc_values": false
         },
         "flastreceivedate": {
            "type": "keyword",
             "index": false,
             "doc_values": false
         },
         "flastreceiveman": {
            "type": "keyword",
            "index": false,
            "doc_values": false
          },
        "fbuyername": {
          "type": "text",
         "analyzer": "standard",
         "doc_values": false
        },
       "projectNumber": {
          "type": "keyword",
          "index": false,
          "doc_values": false
        },
       "fbuyercontactman": {
          "type": "keyword",
          "index": false,
          "doc_values": false
        },
       "fbiderdeliveryplace": {
           "type": "text",
           "analyzer": "ik_max_word",
           "doc_values": false
        },
       "fusername": {
          "type": "text",
         "analyzer": "standard",
         "doc_values": false
        },
        "fbuyerid": {
          "type": "keyword"
        },
       "update_time": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss"
        },
        "return_amount": {
           "type": "scaled_float",
           "scaling_factor": 100,
           "index": false,
           "doc_values": false
       },
       "statement_id": {
          "type": "keyword",
          "doc_values": false
        },
       "relateInfo": {
          "type": "keyword",
          "doc_values": false
        },
        "is_confirm": {
          "type": "keyword",
          "doc_values": false
        },
         "confirm_type": {
          "type": "keyword",
          "doc_values": false
        },
       "summary_no": {
          "type": "text",
         "analyzer": "my_simple",
         "doc_values": false
        },
       "extra_info": {
          "type": "text",
         "analyzer": "my_simple",
         "doc_values": false
        },
       "extra_statement": {
          "type": "text",
         "analyzer": "my_simple",
         "doc_values": false
        },
      "banlance_number": {
          "type": "text",
         "analyzer": "my_simple",
         "doc_values": false
        },
      "fbuyapplicationno": {
          "type": "text",
         "analyzer": "my_simple",
         "doc_values": false
        },
       "department_parent_name": {
          "type": "text",
         "analyzer": "ik_max_word",
         "doc_values": false
        },
        "department_parent_id": {
           "type": "keyword",
           "doc_values": false
        },
        "bid_order_id":{
           "type": "keyword",
           "doc_values": false
        },
        "card": {
          "type": "nested",
          "properties": {
            "card_id": {
              "type": "keyword",
              "doc_values": false
             },
             "card_no": {
              "type": "keyword",
              "doc_values": false
             }
            }
          },
        "log": {
          "type": "nested",
          "properties": {
            "log_id": {
              "type": "long",
              "index": false,
              "doc_values": false
            },
            "approve_status": {
              "type": "keyword",
              "doc_values": false
            },
             "operator_id": {
              "type": "keyword",
              "doc_values": false
            },
             "creation_time": {
                 "type": "date",
                 "format": "yyyy-MM-dd HH:mm:ss",
                 "doc_values": false
            }
           }
        },
        "order_detail": {
          "type": "nested",
          "properties": {
            "detail_id": {
              "type": "keyword",
              "doc_values": false
            },
            "categoryID": {
              "type": "keyword"
            },
            "category_directory_id": {
              "type": "keyword"
            },
          "product_id": {
              "type": "keyword",
              "eager_global_ordinals": true
            },
         "cas_no": {
              "type": "keyword",
              "doc_values": false
            },
          "dangerous_type": {
              "type": "keyword",
              "doc_values": false
            },
          "regulatory_type": {
              "type": "keyword",
              "doc_values": false
            },
            "fgoodcode": {
              "type": "text",
              "analyzer": "ik_max_word",
              "doc_values": false
            },
            "fbrandid": {
              "type": "keyword",
              "eager_global_ordinals": true
            },
           "fbrand": {
              "type": "text",
              "analyzer": "ik_max_word",
              "doc_values": false
            },
           "fgoodname": {
              "type": "text",
              "analyzer": "ik_max_word",
              "doc_values": false
            },
            "fspec": {
              "type": "keyword",
              "index": false,
              "doc_values": false
            },
            "funit":{
                "type": "keyword",
                "index": false,
                "doc_values": false
             },
            "fquantity": {
               "type": "scaled_float",
               "scaling_factor": 100,
               "index": false
            },
            "return_status": {
              "type": "keyword"
            },
           "original_amount": {
             "type": "scaled_float",
            "scaling_factor": 100,
            "index": false
          },
          "original_price": {
             "type": "scaled_float",
            "scaling_factor": 100,
            "index": false
           },
          "fbidamount": {
            "type": "scaled_float",
            "scaling_factor": 100,
            "index": false
            },
           "fcancelquantity": {
             "type": "scaled_float",
              "scaling_factor": 100,
              "index": false
            },
           "fclassification": {
              "type": "keyword",
              "doc_values": false
           },
          "fbidprice": {
           "type": "scaled_float",
           "scaling_factor": 100,
           "index": false,
           "doc_values": false
           }
          }
        }
      }
    }
  }
}'