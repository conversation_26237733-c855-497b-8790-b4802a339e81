<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>msharp-search-sync-order-service</artifactId>
        <groupId>com.ruijing.sync</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>store-search-sync-order-server</artifactId>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ruijing.order</groupId>
            <artifactId>order-base-client</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>inf-bom-data-sync-spring-boot-starter</artifactId>
            <version>${msharp.version}</version>
            <type>pom</type>
        </dependency>
        <!--        自研消息队列-->
        <dependency>
            <groupId>com.ruijing.base</groupId>
            <artifactId>base-biz-mq-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <!--        订单服务-->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-order-api</artifactId>
            <version>1.0.6-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.reagent.local.deploy</groupId>
            <artifactId>local-deploy-platform-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.reagent.order</groupId>
            <artifactId>order-galaxy-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--用户中心-->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-user-api</artifactId>
            <version>1.0.25-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.sync</groupId>
            <artifactId>store-search-sync-order-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing</groupId>
            <artifactId>store-apply-api</artifactId>
            <version>1.5.1-SNAPSHOT</version>
        </dependency>

        <!--        结算单rpc-->
        <dependency>
            <groupId>com.reagent.research</groupId>
            <artifactId>research-statement-api</artifactId>
            <version>3.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-api</artifactId>
            <version>${msharp.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.ruijing.order</groupId>
            <artifactId>order-whitehole-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.base</groupId>
            <version>1.0.0-SNAPSHOT</version>
            <artifactId>base-biz-logger-logback</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ruijing.job</groupId>
            <artifactId>msharp-job-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-organization-api</artifactId>
            <version>1.0.25-SNAPSHOT</version>
        </dependency>
    </dependencies>


    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <!--<plugin>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    &lt;!&ndash;如果想在没有web.xml文件的情况下构建WAR，请设置为false&ndash;&gt;
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    &lt;!&ndash;设置war包的名字&ndash;&gt;
                    <warName>store-order-service</warName>
                </configuration>
            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>