package com.ruijing.sync.common.api;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.sync.common.dto.SyncDataRequestDTO;

/**
 * @Author: <PERSON><PERSON>
 * @Description:
 * @DateTime: 2022/8/17 16:50
 */
public interface CompensateDataRPCService {

    /**
     * 同步数据的RPC基础接口
     * @param request
     * @return 是否同步成功
     */
    RemoteResponse<Boolean> compensateAllData(SyncDataRequestDTO request);
}
