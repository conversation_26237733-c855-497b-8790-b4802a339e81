package com.ruijing.sync.common.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 同步数据请求DTO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/4 16:53
 **/
public class SyncDataRequestDTO implements Serializable {

    private static final long serialVersionUID = -5561286634775060592L;

    /**
     * 需要手动同步最近 ${lastFewDays} 天的数据
     */
    private Integer lastFewDays;

    /**
     * 需要手动同步最近 ${lastHours} 小时的数据
     */
    private Integer lastHours;

    /**
     * 需要手动同步的单号/卡号集合
     */
    private List<String> numberList;

    /**
     * 同步之前${gapMinutes}分钟的数据（当前仅用于数据库和es的比对中）
     */
    private Integer lastMinutes;

    /**
     * 请求密文，需要的额外信息都可以往这里塞，也可以用于权限校验
     */
    private String slang;

    public Integer getLastFewDays() {
        return lastFewDays;
    }

    public void setLastFewDays(Integer lastFewDays) {
        this.lastFewDays = lastFewDays;
    }

    public List<String> getNumberList() {
        return numberList;
    }

    public void setNumberList(List<String> numberList) {
        this.numberList = numberList;
    }

    public Integer getLastHours() {
        return lastHours;
    }

    public void setLastHours(Integer lastHours) {
        this.lastHours = lastHours;
    }

    public Integer getLastMinutes() {
        return lastMinutes;
    }

    public void setLastMinutes(Integer lastMinutes) {
        this.lastMinutes = lastMinutes;
    }

    public String getSlang() {
        return slang;
    }

    public void setSlang(String slang) {
        this.slang = slang;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("SyncDataRequestDTO{");
        sb.append("lastFewDays=").append(lastFewDays);
        sb.append(", lastHours=").append(lastHours);
        sb.append(", numberList=").append(numberList);
        sb.append(", lastMinutes=").append(lastMinutes);
        sb.append(", slang='").append(slang).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
