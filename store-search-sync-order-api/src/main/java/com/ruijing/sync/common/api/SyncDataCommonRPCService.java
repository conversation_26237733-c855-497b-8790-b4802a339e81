package com.ruijing.sync.common.api;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.sync.common.dto.SyncDataRequestDTO;

import java.util.List;

public interface SyncDataCommonRPCService {

    /**
     * 同步数据的RPC基础接口
     * @param request
     * @return 是否同步成功
     */
    RemoteResponse<Boolean> syncData(SyncDataRequestDTO request);

    /**
     * 检查特定时间段es与mysql数据库 订单状态或存在情况 不同的地方
     * @param request
     * @return 不同的订单id列表
     */
    default RemoteResponse<List<Integer>> checkDBAndES(SyncDataRequestDTO request) {
        return null;
    }

    /**
     * 检查特定时间段es与mysql数据库不同的地方并写到excel文件，存到order galaxy掌管的导出数据库中
     * @param request
     * @return
     */
    default RemoteResponse<Boolean> checkDBAndESWriteToExcel(SyncDataRequestDTO request) {
        return null;
    }
}
