package com.ruijing.sync.order.api;

import com.ruijing.fundamental.api.annotation.ServiceDeprecated;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.sync.order.dto.SyncParamDTO;

/**
 * @description: 同步服务RPC接口
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/11/11 11:12
 **/
@Deprecated
public interface SyncOrderRPCService {
    /**
     * 手动同步订单数据RPC接口
     * @param syncParamDTO
     * @return
     */
    RemoteResponse<Boolean> syncOrderData(SyncParamDTO syncParamDTO);
}
