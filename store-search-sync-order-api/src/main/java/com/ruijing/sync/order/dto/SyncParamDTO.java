package com.ruijing.sync.order.dto;


import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/11/11 10:49
 **/
@Deprecated
public class SyncParamDTO implements Serializable {
    private static final long serialVersionUID = 8420689081595659868L;

    /**
     * 同步数据取得最近的天数
     */
    private Long lastFewDays;

    /**
     * 订单号集合
     */
    private List<String> orderNoList;

    public Long getLastFewDays() {
        return lastFewDays;
    }

    public void setLastFewDays(Long lastFewDays) {
        this.lastFewDays = lastFewDays;
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("SyncParamDTO{");
        sb.append("lastFewDays=").append(lastFewDays);
        sb.append(", orderNoList=").append(orderNoList);
        sb.append('}');
        return sb.toString();
    }
}
