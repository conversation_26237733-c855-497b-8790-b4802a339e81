[2025-07-30 16:28:51.939] [INFO ] [main] [...DtpClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A The DtpClient is started successfully.
[2025-07-30 16:28:51.944] [INFO ] [main] [...DtpLifecycleManager] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Thread pool name TraceMonitorManager registered successfully.
[2025-07-30 16:28:52.417] [INFO ] [main] [...PearlConfigManager] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A MSharp-Pearl [Config is Loaded ]
[2025-07-30 16:28:52.472] [WARN ] [main] [...PearlConfigurer] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A class found is interface: org.springframework.boot.validation.beanvalidation.MethodValidationExcludeFilter
[2025-07-30 16:28:52.506] [INFO ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Bean 'com.ruijing.fundamental.springboot.autoconfigure.configuration.ServiceConfiguration' of type [com.ruijing.fundamental.springboot.autoconfigure.configuration.ServiceConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-07-30 16:28:52.519] [INFO ] [main] [...MSharpConfiguration] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A MSharp-RPC [Service Annotation is Started]
[2025-07-30 16:28:52.756] [INFO ] [main] [...NettyServletContext] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Initializing Spring embedded WebApplicationContext
[2025-07-30 16:28:52.756] [INFO ] [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Root WebApplicationContext: initialization completed in 2379 ms
[2025-07-30 16:28:52.797] [INFO ] [main] [...EmbedService] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Multi Protocol Server [Kernel Netty] Initialized With Port(s): [8080],【此Server是支持多协议[Web Http, MSharp-RPC, WebSocket等]的多功能服务器】.
[2025-07-30 16:28:52.924] [WARN ] [main] [...MSharp-Cache] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A MSharp-Cache [Started Cache Client ]
[2025-07-30 16:28:53.070] [ERROR] [main] [c.r.f.m.c.service.ConfigServiceImpl] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A topicConfig:{"data":{"id":18,"appkey":"store-search-sync-order-service","topic":"canal_order_test","write":0,"read":1,"clusterId":1},"message":"成功","code":0}
[2025-07-30 16:28:53.111] [ERROR] [main] [c.r.f.m.c.service.ConfigServiceImpl] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A clusterConfig:{"data":{"clusterId":1,"clusterName":"默认集群","bootstrapServers":"*************:9092,*************:9092,*************:9092","kafkaVersion":"2.1","brokerNum":3,"topicNum":233,"gmtCreate":1585643626000,"gmtModify":1585643626000},"message":"成功","code":0}
[2025-07-30 16:28:53.251] [WARN ] [main] [...CatConfiguration] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A MSharp-Cat [Cat Annotation Aspect is Loaded]
[2025-07-30 16:28:53.661] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.clients.consumer.ConsumerConfig] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 2000
	auto.include.jmx.reporter = true
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092, *************:9092, *************:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = 192.168.4.236_store-search-sync-order-service_dev_1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = store-search-sync-order-service_dev
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_committed
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 5125
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 40000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

[2025-07-30 16:28:53.738] [INFO ] [main] [...MetricClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A start push metric data thread client.
[2025-07-30 16:28:53.876] [INFO ] [canal-client-sync-event-polling-1] [o.a.kafka.common.utils.AppInfoParser] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Kafka version: 3.4.0
[2025-07-30 16:28:53.876] [INFO ] [canal-client-sync-event-polling-1] [o.a.kafka.common.utils.AppInfoParser] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Kafka commitId: 2e1947d240607d53
[2025-07-30 16:28:53.876] [INFO ] [canal-client-sync-event-polling-1] [o.a.kafka.common.utils.AppInfoParser] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Kafka startTimeMs: 1753864133875
[2025-07-30 16:28:53.879] [INFO ] [canal-client-sync-event-polling-1] [o.a.kafka.clients.consumer.KafkaConsumer] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Subscribed to topic(s): canal_order_test
[2025-07-30 16:28:54.401] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting the last seen epoch of partition canal_order_test-0 to 777 since the associated topicId changed from null to TkyftHuIR5uii7ubzmJgcQ
[2025-07-30 16:28:54.402] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting the last seen epoch of partition canal_order_test-4 to 593 since the associated topicId changed from null to TkyftHuIR5uii7ubzmJgcQ
[2025-07-30 16:28:54.402] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting the last seen epoch of partition canal_order_test-1 to 2884 since the associated topicId changed from null to TkyftHuIR5uii7ubzmJgcQ
[2025-07-30 16:28:54.402] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting the last seen epoch of partition canal_order_test-2 to 4730 since the associated topicId changed from null to TkyftHuIR5uii7ubzmJgcQ
[2025-07-30 16:28:54.402] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting the last seen epoch of partition canal_order_test-3 to 4640 since the associated topicId changed from null to TkyftHuIR5uii7ubzmJgcQ
[2025-07-30 16:28:54.403] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Cluster ID: 6nAH52JATg6OgpcF483tbA
[2025-07-30 16:28:54.405] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
[2025-07-30 16:28:54.408] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] (Re-)joining group
[2025-07-30 16:28:54.500] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Request joining group due to: need to re-join with the given member-id: 192.168.4.236_store-search-sync-order-service_dev_1-3b41f034-8979-4fde-9b3a-3d155d39f0c7
[2025-07-30 16:28:54.500] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Request joining group due to: rebalance failed due to 'The group member needs to have a valid member id before actually entering a consumer group.' (MemberIdRequiredException)
[2025-07-30 16:28:54.500] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] (Re-)joining group
[2025-07-30 16:28:54.518] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully joined group with generation Generation{generationId=1, memberId='192.168.4.236_store-search-sync-order-service_dev_1-3b41f034-8979-4fde-9b3a-3d155d39f0c7', protocol='range'}
[2025-07-30 16:28:54.522] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Finished assignment for group at generation 1: {192.168.4.236_store-search-sync-order-service_dev_1-3b41f034-8979-4fde-9b3a-3d155d39f0c7=Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4])}
[2025-07-30 16:28:54.569] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully synced group in generation Generation{generationId=1, memberId='192.168.4.236_store-search-sync-order-service_dev_1-3b41f034-8979-4fde-9b3a-3d155d39f0c7', protocol='range'}
[2025-07-30 16:28:54.570] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Notifying assignor about the new Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4])
[2025-07-30 16:28:54.574] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Adding newly assigned partitions: canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4
[2025-07-30 16:28:54.597] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Found no committed offset for partition canal_order_test-4
[2025-07-30 16:28:54.597] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Found no committed offset for partition canal_order_test-3
[2025-07-30 16:28:54.597] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Found no committed offset for partition canal_order_test-2
[2025-07-30 16:28:54.597] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Found no committed offset for partition canal_order_test-1
[2025-07-30 16:28:54.597] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Found no committed offset for partition canal_order_test-0
[2025-07-30 16:28:54.689] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.SubscriptionState] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting offset for partition canal_order_test-3 to position FetchPosition{offset=392167, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=4640}}.
[2025-07-30 16:28:54.689] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.SubscriptionState] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting offset for partition canal_order_test-2 to position FetchPosition{offset=533916, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=4730}}.
[2025-07-30 16:28:54.689] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.SubscriptionState] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting offset for partition canal_order_test-0 to position FetchPosition{offset=145781, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 3 rack: null)], epoch=777}}.
[2025-07-30 16:28:54.690] [INFO ] [kafka-coordinator-heartbeat-thread | store-search-sync-order-service_dev] [o.a.k.c.c.internals.SubscriptionState] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting offset for partition canal_order_test-4 to position FetchPosition{offset=388201, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 2 rack: null)], epoch=593}}.
[2025-07-30 16:28:54.690] [INFO ] [kafka-coordinator-heartbeat-thread | store-search-sync-order-service_dev] [o.a.k.c.c.internals.SubscriptionState] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting offset for partition canal_order_test-1 to position FetchPosition{offset=543668, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 2 rack: null)], epoch=2884}}.
[2025-07-30 16:30:05.655] [INFO ] [canal-client-sync-event-polling-1] [...DtpLifecycleManager] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Thread pool name MSharp-Commons-Async-Executor registered successfully.
[2025-07-30 16:30:05.663] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=29546, fbuyercode=null, fdeliveryman=null, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=null, finish_date=null, fassessdate=null, flow_id=1256, original_amount=66.0, fbiderdeliveryplace=上海市上海市黄浦区在那非常非常非常非常非常非常非常非常非常非常非常非常非常非常山卡拉地方-测试地区（22）, flastreceiveman=null, frefuse_cancel_reason=null, id=228600, fbuyername=谯初云(勿动), order_type=0, fconfirmdate=null, receive_pic_urls=null, create_time=2025-07-30 16:20:02.0, forderdate=2025-07-30 16:20:02.0, fdeliveryid=935461, fsuppcode=S0570, payment_amount=0.0, fbuyercontactman=BB, statement_id=null, forderno=DC202507304640101, flastreceivedate=null, tpi_project_id=null, fbuyerid=17901, fassessmanid=null, ftbuyappid=319569, fcanceldate=null, delivery_info=null, statement_status=-1, fbuyeremail=<EMAIL>, fbuydepartmentid=29557, invoice_title_number=null, status=8, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=0, invoice_title=null, fmasterguid=null, inventory_status=0, forderamounttotal=66.0, carry_fee=0.0, fcancelman=null, fusername=深圳湾实验室, update_time=2025-07-30 16:30:05.0, fconfirmman=null, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=17665555555, projectID=null, projectnumber=362001200, fund_status=1, fconfirmmanid=null, fund_type_name=null, fdeliverydate=null, invoice_title_id=0, fuserid=143, fbuydepartment=143测试课题组, delivery_no=null, fsuppname=英潍捷基（上海）贸易有限责任公司, flastreceivemanid=null, species=0, in_state_time=1970-10-10 10:00:00.0, fusercode=SHEN_ZHEN_WAN_SHI_YAN_SHI, fcancelreason=1, fassessman=null, projecttitle=null, fsuppid=570, failed_reason=null}], oldData=[{update_time=2025-07-30 16:20:02.0, fcancelreason=null}]}]
[2025-07-30 16:30:09.104] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:30:09.105] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29557] 
[2025-07-30 16:30:09.190] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29557,"organizationId":143,"name":"143测试课题组","managerId":17901,"departmentType":0,"groupId":null,"parentId":29546,"lev":1,"lft":2,"rgt":3,"creationTime":1638867889000,"updateTime":1748491510000,"guid":"dcee13a4-e4cd-46da-a530-1196bdb3577c","accessDTOList":null}] 
[2025-07-30 16:30:09.190] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:30:09.190] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29546] 
[2025-07-30 16:30:09.216] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29546,"organizationId":143,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":132,"creationTime":1638866347000,"updateTime":1638866347000,"guid":"a30059a2-e6b1-4983-a641-baeb321497f9","accessDTOList":null}] 
[2025-07-30 16:30:13.667] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=29546, fbuyercode=null, fdeliveryman=null, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=null, finish_date=null, fassessdate=null, flow_id=1256, original_amount=66.0, fbiderdeliveryplace=上海市上海市黄浦区在那非常非常非常非常非常非常非常非常非常非常非常非常非常非常山卡拉地方-测试地区（22）, flastreceiveman=null, frefuse_cancel_reason=null, id=228600, fbuyername=谯初云(勿动), order_type=0, fconfirmdate=null, receive_pic_urls=null, create_time=2025-07-30 16:20:02.0, forderdate=2025-07-30 16:20:02.0, fdeliveryid=935461, fsuppcode=S0570, payment_amount=0.0, fbuyercontactman=BB, statement_id=null, forderno=DC202507304640101, flastreceivedate=null, tpi_project_id=null, fbuyerid=17901, fassessmanid=null, ftbuyappid=319569, fcanceldate=null, delivery_info=null, statement_status=-1, fbuyeremail=<EMAIL>, fbuydepartmentid=29557, invoice_title_number=null, status=8, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=0, invoice_title=null, fmasterguid=null, inventory_status=0, forderamounttotal=66.0, carry_fee=0.0, fcancelman=null, fusername=深圳湾实验室, update_time=2025-07-30 16:30:13.0, fconfirmman=null, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=17665555555, projectID=null, projectnumber=362001200, fund_status=1, fconfirmmanid=null, fund_type_name=null, fdeliverydate=null, invoice_title_id=0, fuserid=143, fbuydepartment=143测试课题组, delivery_no=null, fsuppname=英潍捷基（上海）贸易有限责任公司, flastreceivemanid=null, species=0, in_state_time=1970-10-10 10:00:00.0, fusercode=SHEN_ZHEN_WAN_SHI_YAN_SHI, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=570, failed_reason=null}], oldData=[{update_time=2025-07-30 16:30:05.0, fcancelreason=1}]}]
[2025-07-30 16:30:15.269] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:30:15.269] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29557] 
[2025-07-30 16:30:15.292] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29557,"organizationId":143,"name":"143测试课题组","managerId":17901,"departmentType":0,"groupId":null,"parentId":29546,"lev":1,"lft":2,"rgt":3,"creationTime":1638867889000,"updateTime":1748491510000,"guid":"dcee13a4-e4cd-46da-a530-1196bdb3577c","accessDTOList":null}] 
[2025-07-30 16:30:15.293] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:30:15.293] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29546] 
[2025-07-30 16:30:15.318] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29546,"organizationId":143,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":132,"creationTime":1638866347000,"updateTime":1638866347000,"guid":"a30059a2-e6b1-4983-a641-baeb321497f9","accessDTOList":null}] 
[2025-07-30 16:30:23.495] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=16479, fbuyercode=null, fdeliveryman=null, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=null, finish_date=null, fassessdate=null, flow_id=227, original_amount=20.0, fbiderdeliveryplace=广东省广州市天河区test地址, flastreceiveman=胡可可, frefuse_cancel_reason=null, id=190946, fbuyername=胡可可, order_type=0, fconfirmdate=2022-03-23 18:15:57.0, receive_pic_urls=null, create_time=2022-03-23 18:15:56.0, forderdate=2022-03-23 18:15:57.0, fdeliveryid=935382, fsuppcode=U050000232, payment_amount=0.0, fbuyercontactman=test人, statement_id=1513309, forderno=DC202203231702401, flastreceivedate=2022-03-24 09:58:29.0, tpi_project_id=null, fbuyerid=3490, fassessmanid=null, ftbuyappid=270523, fcanceldate=null, delivery_info=null, statement_status=2, fbuyeremail=<EMAIL>, fbuydepartmentid=17020, invoice_title_number=null, status=10, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=0, invoice_title=null, fmasterguid=null, inventory_status=9, forderamounttotal=20.0, carry_fee=0.0, fcancelman=null, fusername=丽可祭地医院, update_time=2025-07-30 16:30:23.0, fconfirmman=null, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=15212345678, projectID=null, projectnumber=222457, fund_status=1, fconfirmmanid=null, fund_type_name=null, fdeliverydate=2022-03-23 18:15:57.0, invoice_title_id=0, fuserid=500, fbuydepartment=陈宏宏部门, delivery_no=null, fsuppname=睿瑞有限公司, flastreceivemanid=3490, species=1, in_state_time=2024-12-17 16:03:35.0, fusercode=XINJIESUANCESHIYIYUANLIKE, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=50000232, failed_reason=null}], oldData=[{update_time=2024-12-17 16:03:59.0, statement_status=1}]}]
[2025-07-30 16:30:23.498] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:30:23.500] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[17020] 
[2025-07-30 16:30:23.526] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":17020,"organizationId":500,"name":"陈宏宏部门","managerId":3491,"departmentType":0,"groupId":null,"parentId":16479,"lev":1,"lft":8,"rgt":9,"creationTime":1581868800000,"updateTime":1653901399000,"guid":"24d77c1a-79f7-4804-87b8-1b4018f62fa2","accessDTOList":null}] 
[2025-07-30 16:30:23.527] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:30:23.527] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[16479] 
[2025-07-30 16:30:23.551] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":16479,"organizationId":500,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":32,"creationTime":1576598400000,"updateTime":1581868800000,"guid":"","accessDTOList":null}] 
[2025-07-30 16:31:18.352] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:31:18.351] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=16479, fbuyercode=null, fdeliveryman=金灿烂, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=2462, finish_date=null, fassessdate=null, flow_id=1777, original_amount=120.0, fbiderdeliveryplace=广东省广州市越秀区琶洲商业广场, flastreceiveman=陈宏宏, frefuse_cancel_reason=null, id=209286, fbuyername=陈宏宏, order_type=0, fconfirmdate=2023-11-03 16:42:23.0, receive_pic_urls=null, create_time=2023-11-03 16:42:16.0, forderdate=2023-11-03 16:42:16.0, fdeliveryid=87666, fsuppcode=S01216, payment_amount=0.0, fbuyercontactman=陈宏宏, statement_id=1513362, forderno=DC202311032812601, flastreceivedate=2024-05-24 11:46:10.0, tpi_project_id=null, fbuyerid=3491, fassessmanid=null, ftbuyappid=295568, fcanceldate=null, delivery_info=null, statement_status=8, fbuyeremail=<EMAIL>, fbuydepartmentid=17020, invoice_title_number=null, status=10, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=1, invoice_title=null, fmasterguid=null, inventory_status=8, forderamounttotal=120.0, carry_fee=0.0, fcancelman=null, fusername=丽可医院, update_time=2025-07-30 16:31:18.0, fconfirmman=金灿烂, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=18925016007, projectID=null, projectnumber=gog1, fund_status=1, fconfirmmanid=2462, fund_type_name=TEST_500_1, fdeliverydate=2023-11-03 16:42:26.0, invoice_title_id=0, fuserid=500, fbuydepartment=陈宏宏部门, delivery_no=null, fsuppname=（测试）新金灿烂生物科技有限公司, flastreceivemanid=3491, species=0, in_state_time=2024-12-23 16:22:11.0, fusercode=XINJIESUANCESHIYIYUANLIKE, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=1216, failed_reason=null}], oldData=[{update_time=2025-07-30 16:02:07.0, statement_status=10}]}]
[2025-07-30 16:31:18.353] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[17020] 
[2025-07-30 16:31:18.378] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":17020,"organizationId":500,"name":"陈宏宏部门","managerId":3491,"departmentType":0,"groupId":null,"parentId":16479,"lev":1,"lft":8,"rgt":9,"creationTime":1581868800000,"updateTime":1653901399000,"guid":"24d77c1a-79f7-4804-87b8-1b4018f62fa2","accessDTOList":null}] 
[2025-07-30 16:31:18.378] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:31:18.378] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[16479] 
[2025-07-30 16:31:18.411] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":16479,"organizationId":500,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":32,"creationTime":1576598400000,"updateTime":1581868800000,"guid":"","accessDTOList":null}] 
[2025-07-30 16:31:41.293] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:31:41.293] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=16479, fbuyercode=null, fdeliveryman=金灿烂, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=2462, finish_date=null, fassessdate=null, flow_id=1777, original_amount=120.0, fbiderdeliveryplace=广东省广州市越秀区琶洲商业广场, flastreceiveman=陈宏宏, frefuse_cancel_reason=null, id=209286, fbuyername=陈宏宏, order_type=0, fconfirmdate=2023-11-03 16:42:23.0, receive_pic_urls=null, create_time=2023-11-03 16:42:16.0, forderdate=2023-11-03 16:42:16.0, fdeliveryid=87666, fsuppcode=S01216, payment_amount=0.0, fbuyercontactman=陈宏宏, statement_id=1513362, forderno=DC202311032812601, flastreceivedate=2024-05-24 11:46:10.0, tpi_project_id=null, fbuyerid=3491, fassessmanid=null, ftbuyappid=295568, fcanceldate=null, delivery_info=null, statement_status=7, fbuyeremail=<EMAIL>, fbuydepartmentid=17020, invoice_title_number=null, status=10, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=1, invoice_title=null, fmasterguid=null, inventory_status=8, forderamounttotal=120.0, carry_fee=0.0, fcancelman=null, fusername=丽可医院, update_time=2025-07-30 16:31:41.0, fconfirmman=金灿烂, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=18925016007, projectID=null, projectnumber=gog1, fund_status=1, fconfirmmanid=2462, fund_type_name=TEST_500_1, fdeliverydate=2023-11-03 16:42:26.0, invoice_title_id=0, fuserid=500, fbuydepartment=陈宏宏部门, delivery_no=null, fsuppname=（测试）新金灿烂生物科技有限公司, flastreceivemanid=3491, species=0, in_state_time=2024-12-23 16:22:11.0, fusercode=XINJIESUANCESHIYIYUANLIKE, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=1216, failed_reason=null}], oldData=[{update_time=2025-07-30 16:31:18.0, statement_status=8}]}, DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=16479, fbuyercode=null, fdeliveryman=金灿烂, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=2462, finish_date=null, fassessdate=null, flow_id=1777, original_amount=120.0, fbiderdeliveryplace=广东省广州市越秀区琶洲商业广场, flastreceiveman=陈宏宏, frefuse_cancel_reason=null, id=209286, fbuyername=陈宏宏, order_type=0, fconfirmdate=2023-11-03 16:42:23.0, receive_pic_urls=null, create_time=2023-11-03 16:42:16.0, forderdate=2023-11-03 16:42:16.0, fdeliveryid=87666, fsuppcode=S01216, payment_amount=0.0, fbuyercontactman=陈宏宏, statement_id=1513362, forderno=DC202311032812601, flastreceivedate=2024-05-24 11:46:10.0, tpi_project_id=null, fbuyerid=3491, fassessmanid=null, ftbuyappid=295568, fcanceldate=null, delivery_info=null, statement_status=2, fbuyeremail=<EMAIL>, fbuydepartmentid=17020, invoice_title_number=null, status=10, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=1, invoice_title=null, fmasterguid=null, inventory_status=8, forderamounttotal=120.0, carry_fee=0.0, fcancelman=null, fusername=丽可医院, update_time=2025-07-30 16:31:41.0, fconfirmman=金灿烂, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=18925016007, projectID=null, projectnumber=gog1, fund_status=1, fconfirmmanid=2462, fund_type_name=TEST_500_1, fdeliverydate=2023-11-03 16:42:26.0, invoice_title_id=0, fuserid=500, fbuydepartment=陈宏宏部门, delivery_no=null, fsuppname=（测试）新金灿烂生物科技有限公司, flastreceivemanid=3491, species=0, in_state_time=2024-12-23 16:22:11.0, fusercode=XINJIESUANCESHIYIYUANLIKE, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=1216, failed_reason=null}], oldData=[{statement_status=7}]}]
[2025-07-30 16:31:41.295] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[17020] 
[2025-07-30 16:31:41.327] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":17020,"organizationId":500,"name":"陈宏宏部门","managerId":3491,"departmentType":0,"groupId":null,"parentId":16479,"lev":1,"lft":8,"rgt":9,"creationTime":1581868800000,"updateTime":1653901399000,"guid":"24d77c1a-79f7-4804-87b8-1b4018f62fa2","accessDTOList":null}] 
[2025-07-30 16:31:41.328] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:31:41.328] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[16479] 
[2025-07-30 16:31:41.346] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":16479,"organizationId":500,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":32,"creationTime":1576598400000,"updateTime":1581868800000,"guid":"","accessDTOList":null}] 
[2025-07-30 16:31:41.347] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:31:41.348] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[17020] 
[2025-07-30 16:31:41.382] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":17020,"organizationId":500,"name":"陈宏宏部门","managerId":3491,"departmentType":0,"groupId":null,"parentId":16479,"lev":1,"lft":8,"rgt":9,"creationTime":1581868800000,"updateTime":1653901399000,"guid":"24d77c1a-79f7-4804-87b8-1b4018f62fa2","accessDTOList":null}] 
[2025-07-30 16:31:41.383] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:31:41.383] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[16479] 
[2025-07-30 16:31:41.401] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":16479,"organizationId":500,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":32,"creationTime":1576598400000,"updateTime":1581868800000,"guid":"","accessDTOList":null}] 
[2025-07-30 16:32:20.859] [INFO ] [ShutdownHook] [...MetricClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A starting shutdown of push metric data thread.
[2025-07-30 16:32:20.861] [INFO ] [dtpClient_close_thread] [...DtpClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A The system starts to close the DtpClient and reclaim resources.
[2025-07-30 16:32:20.862] [INFO ] [dtpClient_close_thread] [...DtpClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A The DtpClient is successfully close and resources are reclaimed.
[2025-07-30 16:32:20.863] [INFO ] [ShutdownHook] [...MetricClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A shutdown of push metric data thread has completed.
[2025-07-30 16:41:58.360] [INFO ] [main] [...DtpClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A The DtpClient is started successfully.
[2025-07-30 16:41:58.365] [INFO ] [main] [...DtpLifecycleManager] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Thread pool name TraceMonitorManager registered successfully.
[2025-07-30 16:41:58.699] [INFO ] [main] [...PearlConfigManager] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A MSharp-Pearl [Config is Loaded ]
[2025-07-30 16:41:58.741] [WARN ] [main] [...PearlConfigurer] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A class found is interface: org.springframework.boot.validation.beanvalidation.MethodValidationExcludeFilter
[2025-07-30 16:41:58.769] [INFO ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Bean 'com.ruijing.fundamental.springboot.autoconfigure.configuration.ServiceConfiguration' of type [com.ruijing.fundamental.springboot.autoconfigure.configuration.ServiceConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-07-30 16:41:58.784] [INFO ] [main] [...MSharpConfiguration] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A MSharp-RPC [Service Annotation is Started]
[2025-07-30 16:41:59.026] [INFO ] [main] [...NettyServletContext] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Initializing Spring embedded WebApplicationContext
[2025-07-30 16:41:59.027] [INFO ] [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Root WebApplicationContext: initialization completed in 2068 ms
[2025-07-30 16:41:59.068] [INFO ] [main] [...EmbedService] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Multi Protocol Server [Kernel Netty] Initialized With Port(s): [8080],【此Server是支持多协议[Web Http, MSharp-RPC, WebSocket等]的多功能服务器】.
[2025-07-30 16:41:59.193] [WARN ] [main] [...MSharp-Cache] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A MSharp-Cache [Started Cache Client ]
[2025-07-30 16:41:59.324] [ERROR] [main] [c.r.f.m.c.service.ConfigServiceImpl] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A topicConfig:{"data":{"id":18,"appkey":"store-search-sync-order-service","topic":"canal_order_test","write":0,"read":1,"clusterId":1},"message":"成功","code":0}
[2025-07-30 16:41:59.355] [ERROR] [main] [c.r.f.m.c.service.ConfigServiceImpl] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A clusterConfig:{"data":{"clusterId":1,"clusterName":"默认集群","bootstrapServers":"*************:9092,*************:9092,*************:9092","kafkaVersion":"2.1","brokerNum":3,"topicNum":233,"gmtCreate":1585643626000,"gmtModify":1585643626000},"message":"成功","code":0}
[2025-07-30 16:41:59.383] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.clients.consumer.ConsumerConfig] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 2000
	auto.include.jmx.reporter = true
	auto.offset.reset = latest
	bootstrap.servers = [*************:9092, *************:9092, *************:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = 192.168.4.236_store-search-sync-order-service_dev_1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = store-search-sync-order-service_dev
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_committed
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 5125
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 40000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

[2025-07-30 16:41:59.450] [INFO ] [canal-client-sync-event-polling-1] [o.a.kafka.common.utils.AppInfoParser] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Kafka version: 3.4.0
[2025-07-30 16:41:59.450] [INFO ] [canal-client-sync-event-polling-1] [o.a.kafka.common.utils.AppInfoParser] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Kafka commitId: 2e1947d240607d53
[2025-07-30 16:41:59.450] [INFO ] [canal-client-sync-event-polling-1] [o.a.kafka.common.utils.AppInfoParser] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Kafka startTimeMs: 1753864919449
[2025-07-30 16:41:59.452] [INFO ] [canal-client-sync-event-polling-1] [o.a.kafka.clients.consumer.KafkaConsumer] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Subscribed to topic(s): canal_order_test
[2025-07-30 16:41:59.490] [WARN ] [main] [...CatConfiguration] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A MSharp-Cat [Cat Annotation Aspect is Loaded]
[2025-07-30 16:41:59.780] [INFO ] [main] [...MetricClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A start push metric data thread client.
[2025-07-30 16:41:59.801] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting the last seen epoch of partition canal_order_test-0 to 777 since the associated topicId changed from null to TkyftHuIR5uii7ubzmJgcQ
[2025-07-30 16:41:59.802] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting the last seen epoch of partition canal_order_test-4 to 593 since the associated topicId changed from null to TkyftHuIR5uii7ubzmJgcQ
[2025-07-30 16:41:59.802] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting the last seen epoch of partition canal_order_test-1 to 2884 since the associated topicId changed from null to TkyftHuIR5uii7ubzmJgcQ
[2025-07-30 16:41:59.802] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting the last seen epoch of partition canal_order_test-2 to 4730 since the associated topicId changed from null to TkyftHuIR5uii7ubzmJgcQ
[2025-07-30 16:41:59.802] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Resetting the last seen epoch of partition canal_order_test-3 to 4640 since the associated topicId changed from null to TkyftHuIR5uii7ubzmJgcQ
[2025-07-30 16:41:59.803] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.Metadata] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Cluster ID: 6nAH52JATg6OgpcF483tbA
[2025-07-30 16:41:59.805] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
[2025-07-30 16:41:59.807] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] (Re-)joining group
[2025-07-30 16:41:59.855] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Request joining group due to: need to re-join with the given member-id: 192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c
[2025-07-30 16:41:59.855] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Request joining group due to: rebalance failed due to 'The group member needs to have a valid member id before actually entering a consumer group.' (MemberIdRequiredException)
[2025-07-30 16:41:59.855] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] (Re-)joining group
[2025-07-30 16:41:59.877] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully joined group with generation Generation{generationId=7, memberId='192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c', protocol='range'}
[2025-07-30 16:41:59.878] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Finished assignment for group at generation 7: {192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c=Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4])}
[2025-07-30 16:41:59.903] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully synced group in generation Generation{generationId=7, memberId='192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c', protocol='range'}
[2025-07-30 16:41:59.904] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Notifying assignor about the new Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4])
[2025-07-30 16:41:59.917] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Adding newly assigned partitions: canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4
[2025-07-30 16:41:59.941] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-4 to the committed offset FetchPosition{offset=388204, offsetEpoch=Optional[593], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 2 rack: null)], epoch=593}}
[2025-07-30 16:41:59.941] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-3 to the committed offset FetchPosition{offset=392170, offsetEpoch=Optional[4640], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=4640}}
[2025-07-30 16:41:59.941] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-2 to the committed offset FetchPosition{offset=533918, offsetEpoch=Optional[4730], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=4730}}
[2025-07-30 16:41:59.941] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-1 to the committed offset FetchPosition{offset=543668, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 2 rack: null)], epoch=2884}}
[2025-07-30 16:41:59.941] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-0 to the committed offset FetchPosition{offset=145781, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 3 rack: null)], epoch=777}}
[2025-07-30 16:44:56.967] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Request joining group due to: group is already rebalancing
[2025-07-30 16:44:56.977] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Revoke previously assigned partitions canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4
[2025-07-30 16:44:56.979] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] (Re-)joining group
[2025-07-30 16:44:56.994] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully joined group with generation Generation{generationId=8, memberId='192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c', protocol='range'}
[2025-07-30 16:44:56.995] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Finished assignment for group at generation 8: {192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c=Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2]), 192.168.4.248_store-search-sync-order-service_dev_1-1d3bdbd8-34d4-4011-8e92-a17fc45b9983=Assignment(partitions=[canal_order_test-3, canal_order_test-4])}
[2025-07-30 16:44:57.019] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully synced group in generation Generation{generationId=8, memberId='192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c', protocol='range'}
[2025-07-30 16:44:57.020] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Notifying assignor about the new Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2])
[2025-07-30 16:44:57.020] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Adding newly assigned partitions: canal_order_test-0, canal_order_test-1, canal_order_test-2
[2025-07-30 16:44:57.036] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-0 to the committed offset FetchPosition{offset=145781, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 3 rack: null)], epoch=777}}
[2025-07-30 16:44:57.036] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-2 to the committed offset FetchPosition{offset=533918, offsetEpoch=Optional[4730], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=4730}}
[2025-07-30 16:44:57.036] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-1 to the committed offset FetchPosition{offset=543668, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 2 rack: null)], epoch=2884}}
[2025-07-30 16:47:24.349] [INFO ] [canal-client-sync-event-polling-1] [...DtpLifecycleManager] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A Thread pool name MSharp-Commons-Async-Executor registered successfully.
[2025-07-30 16:47:24.361] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{order_no=DC202507304640001, extra_key_desc=有权限验收审批的人, update_time=2025-07-30 16:47:23.0, create_time=2025-07-30 16:47:23.0, org_id=143, extra_value=[17891,17876,18932,30838,17864,18298,18299,17901,3917,18238], id=182836, extra_key=12, order_id=228599}], oldData=null}, DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=3, type='DELETE'}, sql='null', data=[{order_no=DC202507304640001, extra_key_desc=有权限验收审批的人, update_time=2025-07-30 16:47:23.0, create_time=2025-07-30 16:47:23.0, org_id=143, extra_value=[17891,17876,18932,30838,17864,18298,18299,17901,3917,18238], id=182836, extra_key=12, order_id=228599}], oldData=null}, DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{order_no=DC202507304640001, extra_key_desc=订单验收审批等级, update_time=2025-07-30 16:47:23.0, create_time=2025-07-30 16:47:23.0, org_id=143, extra_value=1, id=182837, extra_key=17, order_id=228599}], oldData=null}, DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=3, type='DELETE'}, sql='null', data=[{order_no=DC202507304640001, extra_key_desc=订单验收审批等级, update_time=2025-07-30 16:47:23.0, create_time=2025-07-30 16:47:23.0, org_id=143, extra_value=1, id=182837, extra_key=17, order_id=228599}], oldData=null}]
[2025-07-30 16:47:24.363] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入OrderMasterRPCClient.findOrderExtraByOrderId 方法, 入参: 
[2025-07-30 16:47:24.365] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[228599] 
[2025-07-30 16:47:24.492] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束OrderMasterRPCClient.findOrderExtraByOrderId 方法,
  出参: [{"id":182824,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":13,"extraKeyDesc":"是否属于试用订单","extraValue":"0","createTime":1753863042000,"updateTime":1753863042000},{"id":182825,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":28,"extraKeyDesc":"商家是否需要填写批次信息","extraValue":"0","createTime":1753863042000,"updateTime":1753863042000},{"id":182827,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":31,"extraKeyDesc":"货仓ID","extraValue":"0","createTime":1753863042000,"updateTime":1753863042000},{"id":182828,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":32,"extraKeyDesc":"货仓类型","extraValue":"0","createTime":1753863042000,"updateTime":1753863042000},{"id":182829,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":34,"extraKeyDesc":"是否老用户下单","extraValue":"1","createTime":1753863042000,"updateTime":1753863042000},{"id":182826,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":40,"extraKeyDesc":"商家是否需要填写商品信息","extraValue":"1","createTime":1753863042000,"updateTime":1753863042000}] 
[2025-07-30 16:47:24.507] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入OrderMasterRPCClient.findOrderExtraByOrderId 方法, 入参: 
[2025-07-30 16:47:24.507] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[228599] 
[2025-07-30 16:47:24.545] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束OrderMasterRPCClient.findOrderExtraByOrderId 方法,
  出参: [{"id":182824,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":13,"extraKeyDesc":"是否属于试用订单","extraValue":"0","createTime":1753863042000,"updateTime":1753863042000},{"id":182825,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":28,"extraKeyDesc":"商家是否需要填写批次信息","extraValue":"0","createTime":1753863042000,"updateTime":1753863042000},{"id":182827,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":31,"extraKeyDesc":"货仓ID","extraValue":"0","createTime":1753863042000,"updateTime":1753863042000},{"id":182828,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":32,"extraKeyDesc":"货仓类型","extraValue":"0","createTime":1753863042000,"updateTime":1753863042000},{"id":182829,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":34,"extraKeyDesc":"是否老用户下单","extraValue":"1","createTime":1753863042000,"updateTime":1753863042000},{"id":182826,"orderId":228599,"orderNo":"DC202507304640001","orgId":143,"extraKey":40,"extraKeyDesc":"商家是否需要填写商品信息","extraValue":"1","createTime":1753863042000,"updateTime":1753863042000}] 
[2025-07-30 16:47:54.167] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Request joining group due to: group is already rebalancing
[2025-07-30 16:47:54.170] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Revoke previously assigned partitions canal_order_test-0, canal_order_test-1, canal_order_test-2
[2025-07-30 16:47:54.170] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] (Re-)joining group
[2025-07-30 16:47:54.188] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully joined group with generation Generation{generationId=9, memberId='192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c', protocol='range'}
[2025-07-30 16:47:54.189] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Finished assignment for group at generation 9: {192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c=Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4])}
[2025-07-30 16:47:54.206] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully synced group in generation Generation{generationId=9, memberId='192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c', protocol='range'}
[2025-07-30 16:47:54.207] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Notifying assignor about the new Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4])
[2025-07-30 16:47:54.207] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Adding newly assigned partitions: canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4
[2025-07-30 16:47:54.224] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-4 to the committed offset FetchPosition{offset=388204, offsetEpoch=Optional[593], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 2 rack: null)], epoch=593}}
[2025-07-30 16:47:54.224] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-3 to the committed offset FetchPosition{offset=392174, offsetEpoch=Optional[4640], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=4640}}
[2025-07-30 16:47:54.224] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-2 to the committed offset FetchPosition{offset=533920, offsetEpoch=Optional[4730], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=4730}}
[2025-07-30 16:47:54.224] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-1 to the committed offset FetchPosition{offset=543670, offsetEpoch=Optional[2884], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 2 rack: null)], epoch=2884}}
[2025-07-30 16:47:54.224] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-0 to the committed offset FetchPosition{offset=145781, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 3 rack: null)], epoch=777}}
[2025-07-30 16:47:54.607] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='t_order_approval_log', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{creation_time=2025-07-30 16:47:23.0, reason=有权限审批, operator_name=null, op_user_type=1, operator_id=17876, approve_status=1, approve_level=1, photo=null, id=327889, order_id=228599}], oldData=null}, DmlData{key='canal_order_test', database='sysjj', tableName='t_order_approval_log', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{creation_time=2025-07-30 16:47:23.0, reason=有权限审批, operator_name=null, op_user_type=1, operator_id=17876, approve_status=1, approve_level=2, photo=null, id=327890, order_id=228599}], oldData=null}, DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=29546, fbuyercode=null, fdeliveryman=王宇星, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=570, finish_date=null, fassessdate=null, flow_id=1256, original_amount=66.0, fbiderdeliveryplace=湖北省孝感市云梦县东岗西路街道斜阳街花园2栋1单元501号-全部地区（addasdasda）, flastreceiveman=公芬apitest143, frefuse_cancel_reason=null, id=228599, fbuyername=公芬apitest143, order_type=0, fconfirmdate=2025-07-30 16:46:56.0, receive_pic_urls=null, create_time=2025-07-30 16:10:42.0, forderdate=2025-07-30 16:10:43.0, fdeliveryid=937168, fsuppcode=S0570, payment_amount=0.0, fbuyercontactman=宰枫诚, statement_id=null, forderno=DC202507304640001, flastreceivedate=2025-07-30 16:47:24.0, tpi_project_id=null, fbuyerid=17876, fassessmanid=null, ftbuyappid=319568, fcanceldate=null, delivery_info=213, statement_status=-1, fbuyeremail=<EMAIL>, fbuydepartmentid=29557, invoice_title_number=null, status=6, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=2, invoice_title=null, fmasterguid=null, inventory_status=0, forderamounttotal=66.0, carry_fee=0.0, fcancelman=null, fusername=深圳湾实验室, update_time=2025-07-30 16:47:23.0, fconfirmman=王宇星, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=18495409109, projectID=null, projectnumber=JTJFK002, fund_status=1, fconfirmmanid=570, fund_type_name=测试2, fdeliverydate=2025-07-30 16:47:09.0, invoice_title_id=0, fuserid=143, fbuydepartment=143测试课题组, delivery_no=null, fsuppname=英潍捷基（上海）贸易有限责任公司, flastreceivemanid=17876, species=0, in_state_time=1970-10-10 10:00:00.0, fusercode=SHEN_ZHEN_WAN_SHI_YAN_SHI, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=570, failed_reason=null}], oldData=[{status=20}]}]
[2025-07-30 16:47:54.680] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:47:54.680] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29557] 
[2025-07-30 16:47:54.732] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29557,"organizationId":143,"name":"143测试课题组","managerId":17901,"departmentType":0,"groupId":null,"parentId":29546,"lev":1,"lft":2,"rgt":3,"creationTime":1638867889000,"updateTime":1748491510000,"guid":"dcee13a4-e4cd-46da-a530-1196bdb3577c","accessDTOList":null}] 
[2025-07-30 16:47:54.732] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:47:54.732] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29546] 
[2025-07-30 16:47:54.750] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29546,"organizationId":143,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":132,"creationTime":1638866347000,"updateTime":1638866347000,"guid":"a30059a2-e6b1-4983-a641-baeb321497f9","accessDTOList":null}] 
[2025-07-30 16:48:44.417] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=29546, fbuyercode=null, fdeliveryman=null, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=null, finish_date=null, fassessdate=null, flow_id=1256, original_amount=66.0, fbiderdeliveryplace=湖北省孝感市云梦县东岗西路街道斜阳街花园2栋1单元501号-全部地区（addasdasda）, flastreceiveman=null, frefuse_cancel_reason=null, id=228598, fbuyername=公芬apitest143, order_type=0, fconfirmdate=2025-07-30 16:48:44.0, receive_pic_urls=null, create_time=2025-07-30 16:09:06.0, forderdate=2025-07-30 16:09:06.0, fdeliveryid=937168, fsuppcode=S0570, payment_amount=0.0, fbuyercontactman=宰枫诚, statement_id=null, forderno=DC202507304639901, flastreceivedate=null, tpi_project_id=null, fbuyerid=17876, fassessmanid=null, ftbuyappid=319567, fcanceldate=null, delivery_info=null, statement_status=-1, fbuyeremail=<EMAIL>, fbuydepartmentid=29557, invoice_title_number=null, status=4, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=2, invoice_title=null, fmasterguid=null, inventory_status=0, forderamounttotal=66.0, carry_fee=0.0, fcancelman=null, fusername=深圳湾实验室, update_time=2025-07-30 16:48:44.0, fconfirmman=王宇星, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=18495409109, projectID=null, projectnumber=JTJFK002, fund_status=1, fconfirmmanid=570, fund_type_name=测试2, fdeliverydate=null, invoice_title_id=0, fuserid=143, fbuydepartment=143测试课题组, delivery_no=null, fsuppname=英潍捷基（上海）贸易有限责任公司, flastreceivemanid=null, species=0, in_state_time=1970-10-10 10:00:00.0, fusercode=SHEN_ZHEN_WAN_SHI_YAN_SHI, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=570, failed_reason=null}], oldData=[{update_time=2025-07-30 16:09:06.0, fconfirmman=null, fconfirmmanid=null, fconfirmdate=null, status=8}]}]
[2025-07-30 16:48:44.417] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:48:44.420] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29557] 
[2025-07-30 16:48:44.461] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29557,"organizationId":143,"name":"143测试课题组","managerId":17901,"departmentType":0,"groupId":null,"parentId":29546,"lev":1,"lft":2,"rgt":3,"creationTime":1638867889000,"updateTime":1748491510000,"guid":"dcee13a4-e4cd-46da-a530-1196bdb3577c","accessDTOList":null}] 
[2025-07-30 16:48:44.462] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:48:44.462] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29546] 
[2025-07-30 16:48:44.498] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29546,"organizationId":143,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":132,"creationTime":1638866347000,"updateTime":1638866347000,"guid":"a30059a2-e6b1-4983-a641-baeb321497f9","accessDTOList":null}] 
[2025-07-30 16:48:57.765] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=29546, fbuyercode=null, fdeliveryman=王宇星, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=570, finish_date=null, fassessdate=null, flow_id=1256, original_amount=66.0, fbiderdeliveryplace=湖北省孝感市云梦县东岗西路街道斜阳街花园2栋1单元501号-全部地区（addasdasda）, flastreceiveman=null, frefuse_cancel_reason=null, id=228598, fbuyername=公芬apitest143, order_type=0, fconfirmdate=2025-07-30 16:48:44.0, receive_pic_urls=null, create_time=2025-07-30 16:09:06.0, forderdate=2025-07-30 16:09:06.0, fdeliveryid=937168, fsuppcode=S0570, payment_amount=0.0, fbuyercontactman=宰枫诚, statement_id=null, forderno=DC202507304639901, flastreceivedate=null, tpi_project_id=null, fbuyerid=17876, fassessmanid=null, ftbuyappid=319567, fcanceldate=null, delivery_info=123, statement_status=-1, fbuyeremail=<EMAIL>, fbuydepartmentid=29557, invoice_title_number=null, status=5, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=2, invoice_title=null, fmasterguid=null, inventory_status=0, forderamounttotal=66.0, carry_fee=0.0, fcancelman=null, fusername=深圳湾实验室, update_time=2025-07-30 16:48:57.0, fconfirmman=王宇星, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=18495409109, projectID=null, projectnumber=JTJFK002, fund_status=1, fconfirmmanid=570, fund_type_name=测试2, fdeliverydate=2025-07-30 16:48:55.0, invoice_title_id=0, fuserid=143, fbuydepartment=143测试课题组, delivery_no=null, fsuppname=英潍捷基（上海）贸易有限责任公司, flastreceivemanid=null, species=0, in_state_time=1970-10-10 10:00:00.0, fusercode=SHEN_ZHEN_WAN_SHI_YAN_SHI, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=570, failed_reason=null}], oldData=[{update_time=2025-07-30 16:48:44.0, fdeliverymanid=null, delivery_info=null, fdeliverydate=null, fdeliveryman=null, delivery_no=null, status=4}]}]
[2025-07-30 16:48:57.766] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:48:57.775] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29557] 
[2025-07-30 16:48:57.797] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29557,"organizationId":143,"name":"143测试课题组","managerId":17901,"departmentType":0,"groupId":null,"parentId":29546,"lev":1,"lft":2,"rgt":3,"creationTime":1638867889000,"updateTime":1748491510000,"guid":"dcee13a4-e4cd-46da-a530-1196bdb3577c","accessDTOList":null}] 
[2025-07-30 16:48:57.797] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:48:57.797] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29546] 
[2025-07-30 16:48:57.820] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29546,"organizationId":143,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":132,"creationTime":1638866347000,"updateTime":1638866347000,"guid":"a30059a2-e6b1-4983-a641-baeb321497f9","accessDTOList":null}] 
[2025-07-30 16:49:13.121] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入OrderMasterRPCClient.findOrderExtraByOrderId 方法, 入参: 
[2025-07-30 16:49:13.121] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{order_no=DC202507304639901, extra_key_desc=有权限验收审批的人, update_time=2025-07-30 16:49:13.0, create_time=2025-07-30 16:49:13.0, org_id=143, extra_value=[17891,17876,18932,30838,17864,18298,18299,17901,3917,18238], id=182838, extra_key=12, order_id=228598}], oldData=null}]
[2025-07-30 16:49:13.123] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[228598] 
[2025-07-30 16:49:13.147] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束OrderMasterRPCClient.findOrderExtraByOrderId 方法,
  出参: [{"id":182838,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":12,"extraKeyDesc":"有权限验收审批的人","extraValue":"[17891,17876,18932,30838,17864,18298,18299,17901,3917,18238]","createTime":1753865353000,"updateTime":1753865353000},{"id":182818,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":13,"extraKeyDesc":"是否属于试用订单","extraValue":"0","createTime":1753862946000,"updateTime":1753862946000},{"id":182839,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":17,"extraKeyDesc":"订单验收审批等级","extraValue":"1","createTime":1753865353000,"updateTime":1753865353000},{"id":182819,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":28,"extraKeyDesc":"商家是否需要填写批次信息","extraValue":"0","createTime":1753862946000,"updateTime":1753862946000},{"id":182821,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":31,"extraKeyDesc":"货仓ID","extraValue":"0","createTime":1753862946000,"updateTime":1753862946000},{"id":182822,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":32,"extraKeyDesc":"货仓类型","extraValue":"0","createTime":1753862946000,"updateTime":1753862946000},{"id":182823,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":34,"extraKeyDesc":"是否老用户下单","extraValue":"1","createTime":1753862946000,"updateTime":1753862946000},{"id":182820,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":40,"extraKeyDesc":"商家是否需要填写商品信息","extraValue":"1","createTime":1753862946000,"updateTime":1753862946000}] 
[2025-07-30 16:49:13.202] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='t_order_approval_log', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{creation_time=2025-07-30 16:49:13.0, reason=null, operator_name=null, op_user_type=0, operator_id=17876, approve_status=6, approve_level=0, photo=null, id=327891, order_id=228598}], oldData=null}, DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=29546, fbuyercode=null, fdeliveryman=王宇星, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=570, finish_date=null, fassessdate=null, flow_id=1256, original_amount=66.0, fbiderdeliveryplace=湖北省孝感市云梦县东岗西路街道斜阳街花园2栋1单元501号-全部地区（addasdasda）, flastreceiveman=公芬apitest143, frefuse_cancel_reason=null, id=228598, fbuyername=公芬apitest143, order_type=0, fconfirmdate=2025-07-30 16:48:44.0, receive_pic_urls=null, create_time=2025-07-30 16:09:06.0, forderdate=2025-07-30 16:09:06.0, fdeliveryid=937168, fsuppcode=S0570, payment_amount=0.0, fbuyercontactman=宰枫诚, statement_id=null, forderno=DC202507304639901, flastreceivedate=2025-07-30 16:49:13.0, tpi_project_id=null, fbuyerid=17876, fassessmanid=null, ftbuyappid=319567, fcanceldate=null, delivery_info=123, statement_status=-1, fbuyeremail=<EMAIL>, fbuydepartmentid=29557, invoice_title_number=null, status=20, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=2, invoice_title=null, fmasterguid=null, inventory_status=0, forderamounttotal=66.0, carry_fee=0.0, fcancelman=null, fusername=深圳湾实验室, update_time=2025-07-30 16:49:13.0, fconfirmman=王宇星, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=18495409109, projectID=null, projectnumber=JTJFK002, fund_status=1, fconfirmmanid=570, fund_type_name=测试2, fdeliverydate=2025-07-30 16:48:55.0, invoice_title_id=0, fuserid=143, fbuydepartment=143测试课题组, delivery_no=null, fsuppname=英潍捷基（上海）贸易有限责任公司, flastreceivemanid=17876, species=0, in_state_time=1970-10-10 10:00:00.0, fusercode=SHEN_ZHEN_WAN_SHI_YAN_SHI, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=570, failed_reason=null}], oldData=[{update_time=2025-07-30 16:48:57.0, flastreceivemanid=null, flastreceiveman=null, status=5, flastreceivedate=null}]}, DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{order_no=DC202507304639901, extra_key_desc=订单验收审批等级, update_time=2025-07-30 16:49:13.0, create_time=2025-07-30 16:49:13.0, org_id=143, extra_value=1, id=182839, extra_key=17, order_id=228598}], oldData=null}]
[2025-07-30 16:49:13.226] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:49:13.226] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29557] 
[2025-07-30 16:49:13.255] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29557,"organizationId":143,"name":"143测试课题组","managerId":17901,"departmentType":0,"groupId":null,"parentId":29546,"lev":1,"lft":2,"rgt":3,"creationTime":1638867889000,"updateTime":1748491510000,"guid":"dcee13a4-e4cd-46da-a530-1196bdb3577c","accessDTOList":null}] 
[2025-07-30 16:49:13.255] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:49:13.255] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29546] 
[2025-07-30 16:49:13.278] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29546,"organizationId":143,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":132,"creationTime":1638866347000,"updateTime":1638866347000,"guid":"a30059a2-e6b1-4983-a641-baeb321497f9","accessDTOList":null}] 
[2025-07-30 16:49:13.279] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入OrderMasterRPCClient.findOrderExtraByOrderId 方法, 入参: 
[2025-07-30 16:49:13.280] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[228598] 
[2025-07-30 16:49:13.296] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束OrderMasterRPCClient.findOrderExtraByOrderId 方法,
  出参: [{"id":182818,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":13,"extraKeyDesc":"是否属于试用订单","extraValue":"0","createTime":1753862946000,"updateTime":1753862946000},{"id":182819,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":28,"extraKeyDesc":"商家是否需要填写批次信息","extraValue":"0","createTime":1753862946000,"updateTime":1753862946000},{"id":182821,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":31,"extraKeyDesc":"货仓ID","extraValue":"0","createTime":1753862946000,"updateTime":1753862946000},{"id":182822,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":32,"extraKeyDesc":"货仓类型","extraValue":"0","createTime":1753862946000,"updateTime":1753862946000},{"id":182823,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":34,"extraKeyDesc":"是否老用户下单","extraValue":"1","createTime":1753862946000,"updateTime":1753862946000},{"id":182820,"orderId":228598,"orderNo":"DC202507304639901","orgId":143,"extraKey":40,"extraKeyDesc":"商家是否需要填写商品信息","extraValue":"1","createTime":1753862946000,"updateTime":1753862946000}] 
[2025-07-30 16:49:14.271] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=3, type='DELETE'}, sql='null', data=[{order_no=DC202507304639901, extra_key_desc=有权限验收审批的人, update_time=2025-07-30 16:49:13.0, create_time=2025-07-30 16:49:13.0, org_id=143, extra_value=[17891,17876,18932,30838,17864,18298,18299,17901,3917,18238], id=182838, extra_key=12, order_id=228598}], oldData=null}, DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=3, type='DELETE'}, sql='null', data=[{order_no=DC202507304639901, extra_key_desc=订单验收审批等级, update_time=2025-07-30 16:49:13.0, create_time=2025-07-30 16:49:13.0, org_id=143, extra_value=1, id=182839, extra_key=17, order_id=228598}], oldData=null}]
[2025-07-30 16:49:14.358] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='t_order_approval_log', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{creation_time=2025-07-30 16:49:13.0, reason=有权限审批, operator_name=null, op_user_type=1, operator_id=17876, approve_status=1, approve_level=1, photo=null, id=327892, order_id=228598}], oldData=null}, DmlData{key='canal_order_test', database='sysjj', tableName='t_order_approval_log', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{creation_time=2025-07-30 16:49:13.0, reason=有权限审批, operator_name=null, op_user_type=1, operator_id=17876, approve_status=1, approve_level=2, photo=null, id=327893, order_id=228598}], oldData=null}, DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=29546, fbuyercode=null, fdeliveryman=王宇星, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=570, finish_date=null, fassessdate=null, flow_id=1256, original_amount=66.0, fbiderdeliveryplace=湖北省孝感市云梦县东岗西路街道斜阳街花园2栋1单元501号-全部地区（addasdasda）, flastreceiveman=公芬apitest143, frefuse_cancel_reason=null, id=228598, fbuyername=公芬apitest143, order_type=0, fconfirmdate=2025-07-30 16:48:44.0, receive_pic_urls=null, create_time=2025-07-30 16:09:06.0, forderdate=2025-07-30 16:09:06.0, fdeliveryid=937168, fsuppcode=S0570, payment_amount=0.0, fbuyercontactman=宰枫诚, statement_id=null, forderno=DC202507304639901, flastreceivedate=2025-07-30 16:49:13.0, tpi_project_id=null, fbuyerid=17876, fassessmanid=null, ftbuyappid=319567, fcanceldate=null, delivery_info=123, statement_status=-1, fbuyeremail=<EMAIL>, fbuydepartmentid=29557, invoice_title_number=null, status=6, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=2, invoice_title=null, fmasterguid=null, inventory_status=0, forderamounttotal=66.0, carry_fee=0.0, fcancelman=null, fusername=深圳湾实验室, update_time=2025-07-30 16:49:13.0, fconfirmman=王宇星, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=18495409109, projectID=null, projectnumber=JTJFK002, fund_status=1, fconfirmmanid=570, fund_type_name=测试2, fdeliverydate=2025-07-30 16:48:55.0, invoice_title_id=0, fuserid=143, fbuydepartment=143测试课题组, delivery_no=null, fsuppname=英潍捷基（上海）贸易有限责任公司, flastreceivemanid=17876, species=0, in_state_time=1970-10-10 10:00:00.0, fusercode=SHEN_ZHEN_WAN_SHI_YAN_SHI, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=570, failed_reason=null}], oldData=[{status=20}]}]
[2025-07-30 16:49:14.399] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:49:14.399] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29557] 
[2025-07-30 16:49:14.416] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29557,"organizationId":143,"name":"143测试课题组","managerId":17901,"departmentType":0,"groupId":null,"parentId":29546,"lev":1,"lft":2,"rgt":3,"creationTime":1638867889000,"updateTime":1748491510000,"guid":"dcee13a4-e4cd-46da-a530-1196bdb3577c","accessDTOList":null}] 
[2025-07-30 16:49:14.417] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:49:14.417] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29546] 
[2025-07-30 16:49:14.441] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29546,"organizationId":143,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":132,"creationTime":1638866347000,"updateTime":1638866347000,"guid":"a30059a2-e6b1-4983-a641-baeb321497f9","accessDTOList":null}] 
[2025-07-30 16:49:14.977] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:49:14.976] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=16479, fbuyercode=null, fdeliveryman=苑小小12, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=655, finish_date=null, fassessdate=null, flow_id=1777, original_amount=37.0, fbiderdeliveryplace=广东省广州市天河区test地址, flastreceiveman=胡可可, frefuse_cancel_reason=null, id=207090, fbuyername=胡可可, order_type=0, fconfirmdate=2023-08-08 09:48:15.0, receive_pic_urls=null, create_time=2023-08-07 11:06:00.0, forderdate=2023-08-07 11:06:00.0, fdeliveryid=935382, fsuppcode=S0655, payment_amount=0.0, fbuyercontactman=test人, statement_id=1513310, forderno=DC202303292670801, flastreceivedate=2023-08-15 14:39:48.0, tpi_project_id=null, fbuyerid=3490, fassessmanid=null, ftbuyappid=286829, fcanceldate=null, delivery_info=null, statement_status=10, fbuyeremail=<EMAIL>, fbuydepartmentid=17020, invoice_title_number=null, status=10, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=1, invoice_title=null, fmasterguid=null, inventory_status=8, forderamounttotal=37.0, carry_fee=12.0, fcancelman=null, fusername=丽可医院, update_time=2025-07-30 16:49:14.0, fconfirmman=苑小小12, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=15212345678, projectID=null, projectnumber=aa1, fund_status=1, fconfirmmanid=655, fund_type_name=TEST_500_1, fdeliverydate=2023-08-08 11:55:27.0, invoice_title_id=0, fuserid=500, fbuydepartment=陈宏宏部门, delivery_no=null, fsuppname=宏兴生物信息科技有限公司（测试）, flastreceivemanid=3490, species=0, in_state_time=2024-12-17 16:06:03.0, fusercode=XINJIESUANCESHIYIYUANLIKE, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=655, failed_reason=null}], oldData=[{update_time=2025-06-30 10:05:30.0, statement_status=2}]}]
[2025-07-30 16:49:14.977] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[17020] 
[2025-07-30 16:49:14.997] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":17020,"organizationId":500,"name":"陈宏宏部门","managerId":3491,"departmentType":0,"groupId":null,"parentId":16479,"lev":1,"lft":8,"rgt":9,"creationTime":1581868800000,"updateTime":1653901399000,"guid":"24d77c1a-79f7-4804-87b8-1b4018f62fa2","accessDTOList":null}] 
[2025-07-30 16:49:14.998] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:49:14.999] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[16479] 
[2025-07-30 16:49:15.020] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":16479,"organizationId":500,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":32,"creationTime":1576598400000,"updateTime":1581868800000,"guid":"","accessDTOList":null}] 
[2025-07-30 16:49:25.730] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:49:25.729] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=16479, fbuyercode=null, fdeliveryman=苑小小12, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=655, finish_date=null, fassessdate=null, flow_id=1777, original_amount=37.0, fbiderdeliveryplace=广东省广州市天河区test地址, flastreceiveman=胡可可, frefuse_cancel_reason=null, id=207090, fbuyername=胡可可, order_type=0, fconfirmdate=2023-08-08 09:48:15.0, receive_pic_urls=null, create_time=2023-08-07 11:06:00.0, forderdate=2023-08-07 11:06:00.0, fdeliveryid=935382, fsuppcode=S0655, payment_amount=0.0, fbuyercontactman=test人, statement_id=1513310, forderno=DC202303292670801, flastreceivedate=2023-08-15 14:39:48.0, tpi_project_id=null, fbuyerid=3490, fassessmanid=null, ftbuyappid=286829, fcanceldate=null, delivery_info=null, statement_status=2, fbuyeremail=<EMAIL>, fbuydepartmentid=17020, invoice_title_number=null, status=10, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=1, invoice_title=null, fmasterguid=null, inventory_status=8, forderamounttotal=37.0, carry_fee=12.0, fcancelman=null, fusername=丽可医院, update_time=2025-07-30 16:49:25.0, fconfirmman=苑小小12, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=15212345678, projectID=null, projectnumber=aa1, fund_status=1, fconfirmmanid=655, fund_type_name=TEST_500_1, fdeliverydate=2023-08-08 11:55:27.0, invoice_title_id=0, fuserid=500, fbuydepartment=陈宏宏部门, delivery_no=null, fsuppname=宏兴生物信息科技有限公司（测试）, flastreceivemanid=3490, species=0, in_state_time=2024-12-17 16:06:03.0, fusercode=XINJIESUANCESHIYIYUANLIKE, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=655, failed_reason=null}], oldData=[{update_time=2025-07-30 16:49:14.0, statement_status=10}]}]
[2025-07-30 16:49:25.731] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[17020] 
[2025-07-30 16:49:25.759] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":17020,"organizationId":500,"name":"陈宏宏部门","managerId":3491,"departmentType":0,"groupId":null,"parentId":16479,"lev":1,"lft":8,"rgt":9,"creationTime":1581868800000,"updateTime":1653901399000,"guid":"24d77c1a-79f7-4804-87b8-1b4018f62fa2","accessDTOList":null}] 
[2025-07-30 16:49:25.760] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:49:25.760] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[16479] 
[2025-07-30 16:49:25.775] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":16479,"organizationId":500,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":32,"creationTime":1576598400000,"updateTime":1581868800000,"guid":"","accessDTOList":null}] 
[2025-07-30 16:50:09.257] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Request joining group due to: group is already rebalancing
[2025-07-30 16:50:09.258] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Revoke previously assigned partitions canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4
[2025-07-30 16:50:09.258] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] (Re-)joining group
[2025-07-30 16:50:09.278] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully joined group with generation Generation{generationId=10, memberId='192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c', protocol='range'}
[2025-07-30 16:50:09.279] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Finished assignment for group at generation 10: {192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c=Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2]), 192.168.4.248_store-search-sync-order-service_dev_1-2ed3976d-e2eb-45a2-947a-7afbd482f4ef=Assignment(partitions=[canal_order_test-3, canal_order_test-4])}
[2025-07-30 16:50:09.302] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully synced group in generation Generation{generationId=10, memberId='192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c', protocol='range'}
[2025-07-30 16:50:09.302] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Notifying assignor about the new Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2])
[2025-07-30 16:50:09.303] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Adding newly assigned partitions: canal_order_test-0, canal_order_test-1, canal_order_test-2
[2025-07-30 16:50:09.315] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-0 to the committed offset FetchPosition{offset=145781, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 3 rack: null)], epoch=777}}
[2025-07-30 16:50:09.315] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-2 to the committed offset FetchPosition{offset=533920, offsetEpoch=Optional[4730], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=4730}}
[2025-07-30 16:50:09.315] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-1 to the committed offset FetchPosition{offset=543670, offsetEpoch=Optional[2884], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 2 rack: null)], epoch=2884}}
[2025-07-30 16:51:00.155] [INFO ] [canal-client-sync-event-polling-1] [org.apache.kafka.clients.NetworkClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Node -3 disconnected.
[2025-07-30 16:51:15.168] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=29546, fbuyercode=null, fdeliveryman=null, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=null, finish_date=null, fassessdate=null, flow_id=1256, original_amount=66.0, fbiderdeliveryplace=上海市上海市黄浦区在那非常非常非常非常非常非常非常非常非常非常非常非常非常非常山卡拉地方-测试地区（22）, flastreceiveman=null, frefuse_cancel_reason=null, id=228600, fbuyername=谯初云(勿动), order_type=0, fconfirmdate=2025-07-30 16:51:15.0, receive_pic_urls=null, create_time=2025-07-30 16:20:02.0, forderdate=2025-07-30 16:20:02.0, fdeliveryid=935461, fsuppcode=S0570, payment_amount=0.0, fbuyercontactman=BB, statement_id=null, forderno=DC202507304640101, flastreceivedate=null, tpi_project_id=null, fbuyerid=17901, fassessmanid=null, ftbuyappid=319569, fcanceldate=null, delivery_info=null, statement_status=-1, fbuyeremail=<EMAIL>, fbuydepartmentid=29557, invoice_title_number=null, status=4, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=0, invoice_title=null, fmasterguid=null, inventory_status=0, forderamounttotal=66.0, carry_fee=0.0, fcancelman=null, fusername=深圳湾实验室, update_time=2025-07-30 16:51:15.0, fconfirmman=王宇星, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=17665555555, projectID=null, projectnumber=362001200, fund_status=1, fconfirmmanid=570, fund_type_name=null, fdeliverydate=null, invoice_title_id=0, fuserid=143, fbuydepartment=143测试课题组, delivery_no=null, fsuppname=英潍捷基（上海）贸易有限责任公司, flastreceivemanid=null, species=0, in_state_time=1970-10-10 10:00:00.0, fusercode=SHEN_ZHEN_WAN_SHI_YAN_SHI, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=570, failed_reason=null}], oldData=[{update_time=2025-07-30 16:30:13.0, fconfirmman=null, fconfirmmanid=null, fconfirmdate=null, status=8}]}]
[2025-07-30 16:51:15.170] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:51:15.170] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29557] 
[2025-07-30 16:51:15.189] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29557,"organizationId":143,"name":"143测试课题组","managerId":17901,"departmentType":0,"groupId":null,"parentId":29546,"lev":1,"lft":2,"rgt":3,"creationTime":1638867889000,"updateTime":1748491510000,"guid":"dcee13a4-e4cd-46da-a530-1196bdb3577c","accessDTOList":null}] 
[2025-07-30 16:51:15.190] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:51:15.190] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29546] 
[2025-07-30 16:51:15.206] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29546,"organizationId":143,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":132,"creationTime":1638866347000,"updateTime":1638866347000,"guid":"a30059a2-e6b1-4983-a641-baeb321497f9","accessDTOList":null}] 
[2025-07-30 16:51:25.851] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:51:25.851] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=29546, fbuyercode=null, fdeliveryman=王宇星, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=570, finish_date=null, fassessdate=null, flow_id=1256, original_amount=66.0, fbiderdeliveryplace=上海市上海市黄浦区在那非常非常非常非常非常非常非常非常非常非常非常非常非常非常山卡拉地方-测试地区（22）, flastreceiveman=null, frefuse_cancel_reason=null, id=228600, fbuyername=谯初云(勿动), order_type=0, fconfirmdate=2025-07-30 16:51:15.0, receive_pic_urls=null, create_time=2025-07-30 16:20:02.0, forderdate=2025-07-30 16:20:02.0, fdeliveryid=935461, fsuppcode=S0570, payment_amount=0.0, fbuyercontactman=BB, statement_id=null, forderno=DC202507304640101, flastreceivedate=null, tpi_project_id=null, fbuyerid=17901, fassessmanid=null, ftbuyappid=319569, fcanceldate=null, delivery_info=123, statement_status=-1, fbuyeremail=<EMAIL>, fbuydepartmentid=29557, invoice_title_number=null, status=5, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=0, invoice_title=null, fmasterguid=null, inventory_status=0, forderamounttotal=66.0, carry_fee=0.0, fcancelman=null, fusername=深圳湾实验室, update_time=2025-07-30 16:51:25.0, fconfirmman=王宇星, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=17665555555, projectID=null, projectnumber=362001200, fund_status=1, fconfirmmanid=570, fund_type_name=null, fdeliverydate=2025-07-30 16:51:22.0, invoice_title_id=0, fuserid=143, fbuydepartment=143测试课题组, delivery_no=null, fsuppname=英潍捷基（上海）贸易有限责任公司, flastreceivemanid=null, species=0, in_state_time=1970-10-10 10:00:00.0, fusercode=SHEN_ZHEN_WAN_SHI_YAN_SHI, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=570, failed_reason=null}], oldData=[{update_time=2025-07-30 16:51:15.0, fdeliverymanid=null, delivery_info=null, fdeliverydate=null, fdeliveryman=null, delivery_no=null, status=4}]}]
[2025-07-30 16:51:25.853] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29557] 
[2025-07-30 16:51:25.881] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29557,"organizationId":143,"name":"143测试课题组","managerId":17901,"departmentType":0,"groupId":null,"parentId":29546,"lev":1,"lft":2,"rgt":3,"creationTime":1638867889000,"updateTime":1748491510000,"guid":"dcee13a4-e4cd-46da-a530-1196bdb3577c","accessDTOList":null}] 
[2025-07-30 16:51:25.881] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:51:25.881] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29546] 
[2025-07-30 16:51:25.897] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29546,"organizationId":143,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":132,"creationTime":1638866347000,"updateTime":1638866347000,"guid":"a30059a2-e6b1-4983-a641-baeb321497f9","accessDTOList":null}] 
[2025-07-30 16:51:33.328] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Request joining group due to: group is already rebalancing
[2025-07-30 16:51:33.329] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Revoke previously assigned partitions canal_order_test-0, canal_order_test-1, canal_order_test-2
[2025-07-30 16:51:33.329] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] (Re-)joining group
[2025-07-30 16:51:33.348] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully joined group with generation Generation{generationId=11, memberId='192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c', protocol='range'}
[2025-07-30 16:51:33.348] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Finished assignment for group at generation 11: {192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c=Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4])}
[2025-07-30 16:51:33.367] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Successfully synced group in generation Generation{generationId=11, memberId='192.168.4.236_store-search-sync-order-service_dev_1-7fd0f0ae-5b1c-4aca-b158-e1940688f09c', protocol='range'}
[2025-07-30 16:51:33.367] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Notifying assignor about the new Assignment(partitions=[canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4])
[2025-07-30 16:51:33.367] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Adding newly assigned partitions: canal_order_test-0, canal_order_test-1, canal_order_test-2, canal_order_test-3, canal_order_test-4
[2025-07-30 16:51:33.383] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-4 to the committed offset FetchPosition{offset=388213, offsetEpoch=Optional[593], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 2 rack: null)], epoch=593}}
[2025-07-30 16:51:33.383] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-3 to the committed offset FetchPosition{offset=392181, offsetEpoch=Optional[4640], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=4640}}
[2025-07-30 16:51:33.383] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-2 to the committed offset FetchPosition{offset=533922, offsetEpoch=Optional[4730], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=4730}}
[2025-07-30 16:51:33.383] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-1 to the committed offset FetchPosition{offset=543670, offsetEpoch=Optional[2884], currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 2 rack: null)], epoch=2884}}
[2025-07-30 16:51:33.383] [INFO ] [canal-client-sync-event-polling-1] [o.a.k.c.c.internals.ConsumerCoordinator] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A [Consumer clientId=192.168.4.236_store-search-sync-order-service_dev_1, groupId=store-search-sync-order-service_dev] Setting offset for partition canal_order_test-0 to the committed offset FetchPosition{offset=145781, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 3 rack: null)], epoch=777}}
[2025-07-30 16:51:47.150] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{order_no=DC202507304640101, extra_key_desc=订单验收审批等级, update_time=2025-07-30 16:51:47.0, create_time=2025-07-30 16:51:47.0, org_id=143, extra_value=1, id=182841, extra_key=17, order_id=228600}], oldData=null}]
[2025-07-30 16:51:47.150] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入OrderMasterRPCClient.findOrderExtraByOrderId 方法, 入参: 
[2025-07-30 16:51:47.153] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[228600] 
[2025-07-30 16:51:47.172] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束OrderMasterRPCClient.findOrderExtraByOrderId 方法,
  出参: [{"id":182840,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":12,"extraKeyDesc":"有权限验收审批的人","extraValue":"[17891,17876,18932,30838,17864,18298,18299,17901,3917,18238]","createTime":1753865507000,"updateTime":1753865507000},{"id":182830,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":13,"extraKeyDesc":"是否属于试用订单","extraValue":"0","createTime":1753863602000,"updateTime":1753863602000},{"id":182841,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":17,"extraKeyDesc":"订单验收审批等级","extraValue":"1","createTime":1753865507000,"updateTime":1753865507000},{"id":182831,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":28,"extraKeyDesc":"商家是否需要填写批次信息","extraValue":"0","createTime":1753863602000,"updateTime":1753863602000},{"id":182833,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":31,"extraKeyDesc":"货仓ID","extraValue":"0","createTime":1753863602000,"updateTime":1753863602000},{"id":182834,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":32,"extraKeyDesc":"货仓类型","extraValue":"0","createTime":1753863602000,"updateTime":1753863602000},{"id":182835,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":34,"extraKeyDesc":"是否老用户下单","extraValue":"1","createTime":1753863602000,"updateTime":1753863602000},{"id":182832,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":40,"extraKeyDesc":"商家是否需要填写商品信息","extraValue":"1","createTime":1753863602000,"updateTime":1753863602000}] 
[2025-07-30 16:51:47.213] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{order_no=DC202507304640101, extra_key_desc=有权限验收审批的人, update_time=2025-07-30 16:51:47.0, create_time=2025-07-30 16:51:47.0, org_id=143, extra_value=[17891,17876,18932,30838,17864,18298,18299,17901,3917,18238], id=182840, extra_key=12, order_id=228600}], oldData=null}]
[2025-07-30 16:51:47.213] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入OrderMasterRPCClient.findOrderExtraByOrderId 方法, 入参: 
[2025-07-30 16:51:47.213] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[228600] 
[2025-07-30 16:51:47.228] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束OrderMasterRPCClient.findOrderExtraByOrderId 方法,
  出参: [{"id":182830,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":13,"extraKeyDesc":"是否属于试用订单","extraValue":"0","createTime":1753863602000,"updateTime":1753863602000},{"id":182831,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":28,"extraKeyDesc":"商家是否需要填写批次信息","extraValue":"0","createTime":1753863602000,"updateTime":1753863602000},{"id":182833,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":31,"extraKeyDesc":"货仓ID","extraValue":"0","createTime":1753863602000,"updateTime":1753863602000},{"id":182834,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":32,"extraKeyDesc":"货仓类型","extraValue":"0","createTime":1753863602000,"updateTime":1753863602000},{"id":182835,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":34,"extraKeyDesc":"是否老用户下单","extraValue":"1","createTime":1753863602000,"updateTime":1753863602000},{"id":182832,"orderId":228600,"orderNo":"DC202507304640101","orgId":143,"extraKey":40,"extraKeyDesc":"商家是否需要填写商品信息","extraValue":"1","createTime":1753863602000,"updateTime":1753863602000}] 
[2025-07-30 16:51:47.295] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=3, type='DELETE'}, sql='null', data=[{order_no=DC202507304640101, extra_key_desc=订单验收审批等级, update_time=2025-07-30 16:51:47.0, create_time=2025-07-30 16:51:47.0, org_id=143, extra_value=1, id=182841, extra_key=17, order_id=228600}], oldData=null}, DmlData{key='canal_order_test', database='sysjj', tableName='t_order_approval_log', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{creation_time=2025-07-30 16:51:47.0, reason=null, operator_name=null, op_user_type=0, operator_id=17876, approve_status=6, approve_level=0, photo=null, id=327894, order_id=228600}], oldData=null}, DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=29546, fbuyercode=null, fdeliveryman=王宇星, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=570, finish_date=null, fassessdate=null, flow_id=1256, original_amount=66.0, fbiderdeliveryplace=上海市上海市黄浦区在那非常非常非常非常非常非常非常非常非常非常非常非常非常非常山卡拉地方-测试地区（22）, flastreceiveman=公芬apitest143, frefuse_cancel_reason=null, id=228600, fbuyername=谯初云(勿动), order_type=0, fconfirmdate=2025-07-30 16:51:15.0, receive_pic_urls=null, create_time=2025-07-30 16:20:02.0, forderdate=2025-07-30 16:20:02.0, fdeliveryid=935461, fsuppcode=S0570, payment_amount=0.0, fbuyercontactman=BB, statement_id=null, forderno=DC202507304640101, flastreceivedate=2025-07-30 16:51:47.0, tpi_project_id=null, fbuyerid=17901, fassessmanid=null, ftbuyappid=319569, fcanceldate=null, delivery_info=123, statement_status=-1, fbuyeremail=<EMAIL>, fbuydepartmentid=29557, invoice_title_number=null, status=20, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=0, invoice_title=null, fmasterguid=null, inventory_status=0, forderamounttotal=66.0, carry_fee=0.0, fcancelman=null, fusername=深圳湾实验室, update_time=2025-07-30 16:51:47.0, fconfirmman=王宇星, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=17665555555, projectID=null, projectnumber=362001200, fund_status=1, fconfirmmanid=570, fund_type_name=null, fdeliverydate=2025-07-30 16:51:22.0, invoice_title_id=0, fuserid=143, fbuydepartment=143测试课题组, delivery_no=null, fsuppname=英潍捷基（上海）贸易有限责任公司, flastreceivemanid=17876, species=0, in_state_time=1970-10-10 10:00:00.0, fusercode=SHEN_ZHEN_WAN_SHI_YAN_SHI, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=570, failed_reason=null}], oldData=[{update_time=2025-07-30 16:51:25.0, flastreceivemanid=null, flastreceiveman=null, status=5, flastreceivedate=null}]}, DmlData{key='canal_order_test', database='sysjj', tableName='t_order_approval_log', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{creation_time=2025-07-30 16:51:47.0, reason=有权限审批, operator_name=null, op_user_type=1, operator_id=17876, approve_status=1, approve_level=1, photo=null, id=327895, order_id=228600}], oldData=null}, DmlData{key='canal_order_test', database='sysjj', tableName='t_order_approval_log', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{creation_time=2025-07-30 16:51:47.0, reason=有权限审批, operator_name=null, op_user_type=1, operator_id=17876, approve_status=1, approve_level=2, photo=null, id=327896, order_id=228600}], oldData=null}, DmlData{key='canal_order_test', database='sysjj', tableName='torder_master', eventType=EventType{code=2, type='UPDATE'}, sql='null', data=[{dept_parent_id=29546, fbuyercode=null, fdeliveryman=王宇星, shut_down_date=null, purchase_RootIn_Type=0, fdeliverymanid=570, finish_date=null, fassessdate=null, flow_id=1256, original_amount=66.0, fbiderdeliveryplace=上海市上海市黄浦区在那非常非常非常非常非常非常非常非常非常非常非常非常非常非常山卡拉地方-测试地区（22）, flastreceiveman=公芬apitest143, frefuse_cancel_reason=null, id=228600, fbuyername=谯初云(勿动), order_type=0, fconfirmdate=2025-07-30 16:51:15.0, receive_pic_urls=null, create_time=2025-07-30 16:20:02.0, forderdate=2025-07-30 16:20:02.0, fdeliveryid=935461, fsuppcode=S0570, payment_amount=0.0, fbuyercontactman=BB, statement_id=null, forderno=DC202507304640101, flastreceivedate=2025-07-30 16:51:47.0, tpi_project_id=null, fbuyerid=17901, fassessmanid=null, ftbuyappid=319569, fcanceldate=null, delivery_info=123, statement_status=-1, fbuyeremail=<EMAIL>, fbuydepartmentid=29557, invoice_title_number=null, status=6, frefuse_cancel_date=null, fcancelmanid=null, return_amount=0.0, piEmail=null, fund_type=0, invoice_title=null, fmasterguid=null, inventory_status=0, forderamounttotal=66.0, carry_fee=0.0, fcancelman=null, fusername=深圳湾实验室, update_time=2025-07-30 16:51:47.0, fconfirmman=王宇星, bid_order_id=null, dept_parent_name=根部门, fbuyertelephone=17665555555, projectID=null, projectnumber=362001200, fund_status=1, fconfirmmanid=570, fund_type_name=null, fdeliverydate=2025-07-30 16:51:22.0, invoice_title_id=0, fuserid=143, fbuydepartment=143测试课题组, delivery_no=null, fsuppname=英潍捷基（上海）贸易有限责任公司, flastreceivemanid=17876, species=0, in_state_time=1970-10-10 10:00:00.0, fusercode=SHEN_ZHEN_WAN_SHI_YAN_SHI, fcancelreason=null, fassessman=null, projecttitle=null, fsuppid=570, failed_reason=null}], oldData=[{status=20}]}, DmlData{key='canal_order_test', database='order_galaxy', tableName='order_extra', eventType=EventType{code=3, type='DELETE'}, sql='null', data=[{order_no=DC202507304640101, extra_key_desc=有权限验收审批的人, update_time=2025-07-30 16:51:47.0, create_time=2025-07-30 16:51:47.0, org_id=143, extra_value=[17891,17876,18932,30838,17864,18298,18299,17901,3917,18238], id=182840, extra_key=12, order_id=228600}], oldData=null}]
[2025-07-30 16:51:47.317] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:51:47.317] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29557] 
[2025-07-30 16:51:47.334] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29557,"organizationId":143,"name":"143测试课题组","managerId":17901,"departmentType":0,"groupId":null,"parentId":29546,"lev":1,"lft":2,"rgt":3,"creationTime":1638867889000,"updateTime":1748491510000,"guid":"dcee13a4-e4cd-46da-a530-1196bdb3577c","accessDTOList":null}] 
[2025-07-30 16:51:47.334] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:51:47.334] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29546] 
[2025-07-30 16:51:47.353] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29546,"organizationId":143,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":132,"creationTime":1638866347000,"updateTime":1638866347000,"guid":"a30059a2-e6b1-4983-a641-baeb321497f9","accessDTOList":null}] 
[2025-07-30 16:51:47.395] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:51:47.395] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29557] 
[2025-07-30 16:51:47.413] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29557,"organizationId":143,"name":"143测试课题组","managerId":17901,"departmentType":0,"groupId":null,"parentId":29546,"lev":1,"lft":2,"rgt":3,"creationTime":1638867889000,"updateTime":1748491510000,"guid":"dcee13a4-e4cd-46da-a530-1196bdb3577c","accessDTOList":null}] 
[2025-07-30 16:51:47.413] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入UserRPCClient.findDepartmentByIdList 方法, 入参: 
[2025-07-30 16:51:47.413] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：[29546] 
[2025-07-30 16:51:47.430] [INFO ] [MSharp-Commons-Async-Executor] [UserRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束UserRPCClient.findDepartmentByIdList 方法,
  出参: [{"id":29546,"organizationId":143,"name":"根部门","managerId":null,"departmentType":1,"groupId":null,"parentId":null,"lev":0,"lft":1,"rgt":132,"creationTime":1638866347000,"updateTime":1638866347000,"guid":"a30059a2-e6b1-4983-a641-baeb321497f9","accessDTOList":null}] 
[2025-07-30 16:52:02.660] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='t_docking_extra', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{creation_time=2025-07-30 16:52:03.0, update_time=2025-07-30 16:52:03.0, extra_info=mock_DC202507304640301, memo=null, statusExtra=0, id=177668, type=0, info=DC202507304640301}], oldData=null}]
[2025-07-30 16:52:02.684] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入OrderMasterRPCClient.findByOrderOrderNoList 方法, 入参: 
[2025-07-30 16:52:02.684] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：["DC202507304640301"] 
[2025-07-30 16:52:02.701] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束OrderMasterRPCClient.findByOrderOrderNoList 方法,
  出参: [] 
[2025-07-30 16:52:03.158] [INFO ] [MSharp-Commons-Async-Executor] [c.r.sync.order.client.BaseDataSyncClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A curDataChanged: [DmlData{key='canal_order_test', database='sysjj', tableName='t_docking_extra', eventType=EventType{code=1, type='INSERT'}, sql='null', data=[{creation_time=2025-07-30 16:52:03.0, update_time=2025-07-30 16:52:03.0, extra_info=mock_DC202507304640201, memo=null, statusExtra=0, id=177669, type=0, info=DC202507304640201}], oldData=null}]
[2025-07-30 16:52:03.175] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 进入OrderMasterRPCClient.findByOrderOrderNoList 方法, 入参: 
[2025-07-30 16:52:03.176] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 参数0：["DC202507304640201"] 
[2025-07-30 16:52:03.200] [INFO ] [canal-client-sync-event-polling-1] [OrderMasterRPCClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A 结束OrderMasterRPCClient.findByOrderOrderNoList 方法,
  出参: [] 
[2025-07-30 16:52:35.085] [INFO ] [dtpClient_close_thread] [...DtpClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A The system starts to close the DtpClient and reclaim resources.
[2025-07-30 16:52:35.086] [INFO ] [dtpClient_close_thread] [...DtpClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A The DtpClient is successfully close and resources are reclaimed.
[2025-07-30 16:52:35.087] [INFO ] [ShutdownHook] [...MetricClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A starting shutdown of push metric data thread.
[2025-07-30 16:52:35.087] [INFO ] [ShutdownHook] [...MetricClient] GUID:N/A LOCALID:N/A CODE:N/A SESSION:N/A shutdown of push metric data thread has completed.
